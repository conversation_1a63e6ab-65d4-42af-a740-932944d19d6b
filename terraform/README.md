# Terraform Infrastructure for WebRTC Streaming Platform

This directory contains Terraform configurations for deploying the WebRTC streaming platform infrastructure on Google Cloud Platform.

## Architecture Overview

The infrastructure consists of:

### Core Services
- **Signaling Server**: Cloud Run service for WebRTC signaling and frontend
- **Media Server**: Cloud Run service for WebRTC-to-RTMP processing
- **TURN Server**: Compute Engine instance for NAT traversal

### Supporting Infrastructure
- **Networking**: VPC, subnets, firewall rules
- **Container Registry**: Artifact Registry for Docker images
- **Load Balancing**: Application Load Balancer for high availability
- **Security**: IAM roles, service accounts, Secret Manager
- **Monitoring**: Cloud Logging, Monitoring, and Alerting

## Directory Structure

```
terraform/
├── environments/           # Environment-specific configurations
│   ├── dev/               # Development environment
│   ├── staging/           # Staging environment
│   └── prod/              # Production environment
├── modules/               # Reusable Terraform modules
│   ├── networking/        # VPC, subnets, firewall rules
│   ├── cloud-run/         # Cloud Run services
│   ├── compute/           # Compute Engine instances
│   ├── registry/          # Artifact Registry
│   ├── monitoring/        # Logging and monitoring
│   └── security/          # IAM and security
├── scripts/               # Deployment and utility scripts
├── main.tf               # Main infrastructure definition
├── variables.tf          # Input variables
├── outputs.tf            # Output values
├── versions.tf           # Provider versions
└── terraform.tfvars.example # Example variables file
```

## Prerequisites

1. **Google Cloud SDK**: Install and authenticate
```bash
gcloud auth login
gcloud config set project YOUR_PROJECT_ID
```

2. **Terraform**: Install Terraform >= 1.0
```bash
# macOS
brew install terraform

# Or download from https://terraform.io/downloads
```

3. **Enable Required APIs**:
```bash
gcloud services enable compute.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable artifactregistry.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable logging.googleapis.com
gcloud services enable monitoring.googleapis.com
```

## Quick Start

### 1. Initialize Terraform
```bash
cd terraform
terraform init
```

### 2. Create terraform.tfvars
```bash
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values
```

### 3. Plan and Apply
```bash
# Review the plan
terraform plan

# Apply the infrastructure
terraform apply
```

### 4. Deploy Applications
```bash
# Deploy using the provided scripts
./scripts/deploy-all.sh
```

## Environment Management

### Development Environment
```bash
cd environments/dev
terraform init
terraform apply
```

### Staging Environment
```bash
cd environments/staging
terraform init
terraform apply
```

### Production Environment
```bash
cd environments/prod
terraform init
terraform apply
```

## Configuration

### Required Variables
- `project_id`: Google Cloud Project ID
- `region`: Primary deployment region
- `environment`: Environment name (dev/staging/prod)

### Optional Variables
- `domain_name`: Custom domain for the application
- `turn_server_machine_type`: TURN server instance type
- `enable_monitoring`: Enable advanced monitoring

## Deployment Scripts

### Deploy All Services
```bash
./scripts/deploy-all.sh [environment]
```

### Deploy Individual Services
```bash
./scripts/deploy-signaling.sh [environment]
./scripts/deploy-media-server.sh [environment]
./scripts/deploy-turn-server.sh [environment]
```

### Destroy Infrastructure
```bash
./scripts/destroy.sh [environment]
```

## Monitoring and Maintenance

### View Logs
```bash
# Signaling server logs
gcloud logs tail --service=webrtc-signaling

# Media server logs
gcloud logs tail --service=webrtc-media-server
```

### Check Service Status
```bash
# Cloud Run services
gcloud run services list

# Compute Engine instances
gcloud compute instances list
```

### Update Infrastructure
```bash
# Plan changes
terraform plan

# Apply changes
terraform apply
```

## Security Considerations

1. **Service Accounts**: Each service uses dedicated service accounts with minimal permissions
2. **Network Security**: Firewall rules restrict access to necessary ports only
3. **Secrets Management**: Sensitive data stored in Secret Manager
4. **HTTPS**: All web traffic encrypted with SSL certificates
5. **TURN Security**: TURN server uses authentication and restricted port ranges

## Cost Optimization

1. **Development**: Uses minimal instance sizes and shared resources
2. **Auto-scaling**: Cloud Run scales to zero when not in use
3. **Preemptible Instances**: TURN server can use preemptible instances for cost savings
4. **Resource Monitoring**: Alerts for unexpected resource usage

## Troubleshooting

### Common Issues

1. **API Not Enabled**: Ensure all required APIs are enabled
2. **Permissions**: Check service account permissions
3. **Firewall Rules**: Verify TURN server ports are accessible
4. **Resource Limits**: Check project quotas and limits

### Debug Commands
```bash
# Check Terraform state
terraform show

# Validate configuration
terraform validate

# Check service connectivity
./scripts/test-connectivity.sh
```

## Migration from Current Setup

See [MIGRATION.md](MIGRATION.md) for detailed instructions on migrating from the current bash script deployment to Terraform.
