# Example Terraform variables file
# Copy this file to terraform.tfvars and update with your values

# Required Variables
project_id = "your-gcp-project-id"
region     = "us-central1"
zone       = "us-central1-a"

# Environment Configuration
environment = "dev"  # dev, staging, or prod

# Application Configuration
app_name    = "webrtc-platform"
domain_name = ""  # Optional: your custom domain

# Networking Configuration
vpc_name    = "webrtc-vpc"
subnet_cidr = "10.0.0.0/24"

# Docker Images (will be updated after building)
signaling_server_image = "gcr.io/PROJECT_ID/webrtc-signaling:latest"
media_server_image     = "gcr.io/PROJECT_ID/media-server:latest"

# Cloud Run Configuration
cloud_run_cpu          = "1"
cloud_run_memory       = "2Gi"
cloud_run_max_instances = 10

# TURN Server Configuration
turn_server_machine_type = "e2-small"
turn_server_image       = "coturn/coturn:latest"
turn_username           = "webrtc"
turn_password           = "your-secure-password-here"

# Monitoring Configuration
enable_monitoring  = true
notification_email = "<EMAIL>"

# Security Configuration
allowed_source_ranges = ["0.0.0.0/0"]  # Restrict this in production

# Feature Flags
enable_ssl    = false  # Set to true if using custom domain
enable_cdn    = false
enable_backup = false

# Resource Labels
labels = {
  project     = "webrtc-platform"
  managed-by  = "terraform"
  environment = "dev"
}
