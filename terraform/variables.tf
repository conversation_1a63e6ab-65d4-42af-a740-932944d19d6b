# Project Configuration
variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
}

variable "region" {
  description = "Primary deployment region"
  type        = string
  default     = "us-central1"
}

variable "zone" {
  description = "Primary deployment zone"
  type        = string
  default     = "us-central1-a"
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

# Networking Configuration
variable "vpc_name" {
  description = "Name of the VPC network"
  type        = string
  default     = "webrtc-vpc"
}

variable "subnet_cidr" {
  description = "CIDR block for the subnet"
  type        = string
  default     = "10.0.0.0/24"
}

# Application Configuration
variable "app_name" {
  description = "Application name prefix"
  type        = string
  default     = "webrtc-platform"
}

variable "domain_name" {
  description = "Custom domain name for the application (optional)"
  type        = string
  default     = ""
}

# Cloud Run Configuration
variable "signaling_server_image" {
  description = "Docker image for the signaling server"
  type        = string
  default     = "gcr.io/PROJECT_ID/webrtc-signaling:latest"
}

variable "media_server_image" {
  description = "Docker image for the media server"
  type        = string
  default     = "gcr.io/PROJECT_ID/media-server:latest"
}

variable "cloud_run_cpu" {
  description = "CPU allocation for Cloud Run services"
  type        = string
  default     = "1"
}

variable "cloud_run_memory" {
  description = "Memory allocation for Cloud Run services"
  type        = string
  default     = "2Gi"
}

variable "cloud_run_max_instances" {
  description = "Maximum number of Cloud Run instances"
  type        = number
  default     = 10
}

variable "media_server_machine_type" {
  description = "Machine type for the media server"
  type        = string
  default     = "e2-standard-2"
}

# TURN Server Configuration
variable "turn_server_machine_type" {
  description = "Machine type for the TURN server"
  type        = string
  default     = "e2-small"
}

variable "turn_server_image" {
  description = "Docker image for the TURN server"
  type        = string
  default     = "coturn/coturn:latest"
}

variable "turn_username" {
  description = "TURN server username"
  type        = string
  default     = "webrtc"
  sensitive   = true
}

variable "turn_password" {
  description = "TURN server password"
  type        = string
  default     = "webrtc123"
  sensitive   = true
}

# Monitoring Configuration
variable "enable_monitoring" {
  description = "Enable advanced monitoring and alerting"
  type        = bool
  default     = true
}

variable "notification_email" {
  description = "Email address for monitoring notifications"
  type        = string
  default     = ""
}

# Security Configuration
variable "allowed_source_ranges" {
  description = "CIDR blocks allowed to access the services"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

# Resource Labels
variable "labels" {
  description = "Labels to apply to all resources"
  type        = map(string)
  default = {
    project     = "webrtc-platform"
    managed-by  = "terraform"
  }
}

# Feature Flags
variable "enable_ssl" {
  description = "Enable SSL certificates for custom domains"
  type        = bool
  default     = false
}

variable "enable_cdn" {
  description = "Enable Cloud CDN for static content"
  type        = bool
  default     = false
}

variable "enable_backup" {
  description = "Enable automated backups"
  type        = bool
  default     = false
}
