# Testing and Validation Guide

This guide provides comprehensive testing procedures for the Terraform-deployed WebRTC infrastructure.

## Pre-Deployment Testing

### 1. Validate Terraform Configuration
```bash
cd terraform/environments/dev

# Check syntax and configuration
terraform validate

# Format code
terraform fmt -recursive

# Check for security issues (if tfsec is installed)
tfsec .
```

### 2. Plan Review
```bash
# Generate and review deployment plan
terraform plan -out=tfplan

# Review the plan carefully
terraform show tfplan
```

## Deployment Testing

### 1. Infrastructure Deployment
```bash
# Deploy using the automated script
cd terraform
./scripts/deploy-all.sh dev your-project-id

# Monitor deployment progress
# The script will show progress and any errors
```

### 2. Verify Infrastructure Components
```bash
# Test connectivity to all services
./scripts/test-connectivity.sh dev

# Check Terraform outputs
cd environments/dev
terraform output
```

## Service-Level Testing

### 1. Cloud Run Services

#### Signaling Server
```bash
# Get service URL
SIGNALING_URL=$(cd terraform/environments/dev && terraform output -raw signaling_server_url)

# Test health endpoint
curl -f "$SIGNALING_URL/health" || echo "Health check failed"

# Test main page
curl -f "$SIGNALING_URL" | grep -q "WebRTC" && echo "Main page OK" || echo "Main page failed"

# Check service logs
gcloud logs tail --service=webrtc-platform-dev-signaling --limit=50
```

#### Media Server
```bash
# Get service URL
MEDIA_URL=$(cd terraform/environments/dev && terraform output -raw media_server_url)

# Test health endpoint
curl -f "$MEDIA_URL/health" || echo "Health check failed"

# Check service logs
gcloud logs tail --service=webrtc-platform-dev-media --limit=50
```

### 2. TURN Server

#### Basic Connectivity
```bash
# Get TURN server IP
TURN_IP=$(cd terraform/environments/dev && terraform output -raw turn_server_ip)

# Test TCP connectivity
timeout 5 bash -c "</dev/tcp/$TURN_IP/3478" && echo "TURN TCP OK" || echo "TURN TCP failed"

# Test UDP connectivity (if netcat is available)
timeout 5 nc -u -z "$TURN_IP" 3478 && echo "TURN UDP OK" || echo "TURN UDP test failed"
```

#### TURN Server Functionality
```bash
# SSH to TURN server and check status
gcloud compute ssh webrtc-platform-dev-turn-server --zone=us-central1-a --command="docker ps | grep coturn"

# Check TURN server logs
gcloud compute ssh webrtc-platform-dev-turn-server --zone=us-central1-a --command="docker logs coturn-server --tail=50"
```

### 3. Network Configuration

#### Firewall Rules
```bash
# List firewall rules
gcloud compute firewall-rules list --filter="name:webrtc-platform-dev"

# Test specific ports
nmap -p 3478,5349 "$TURN_IP"  # If nmap is available
```

#### VPC Configuration
```bash
# Check VPC and subnets
gcloud compute networks list --filter="name:webrtc-platform-dev"
gcloud compute networks subnets list --filter="name:webrtc-platform-dev"
```

## Application-Level Testing

### 1. WebRTC Functionality

#### Manual Testing
1. **Open the application**:
   ```bash
   SIGNALING_URL=$(cd terraform/environments/dev && terraform output -raw signaling_server_url)
   echo "Open: $SIGNALING_URL"
   ```

2. **Create a room** and test:
   - Camera and microphone access
   - Room creation and joining
   - Video compositing
   - TURN server connectivity (check browser console)

#### Automated Testing (if test suite exists)
```bash
# Run end-to-end tests
npm run test:e2e

# Run WebRTC connectivity tests
npm run test:webrtc
```

### 2. RTMP Streaming

#### Test Media Server Integration
```bash
# Check if media server can receive WebRTC streams
# This requires manual testing with the application

# Monitor media server logs during streaming
gcloud logs tail --service=webrtc-platform-dev-media --follow
```

## Performance Testing

### 1. Load Testing

#### Cloud Run Services
```bash
# Basic load test (if hey or ab is available)
hey -n 100 -c 10 "$SIGNALING_URL"

# Monitor resource usage
gcloud monitoring metrics list --filter="resource.type=cloud_run_revision"
```

#### TURN Server
```bash
# Monitor TURN server resource usage
gcloud compute instances describe webrtc-platform-dev-turn-server \
  --zone=us-central1-a \
  --format="table(status,machineType,networkInterfaces[0].accessConfigs[0].natIP)"
```

### 2. Scaling Tests

#### Auto-scaling Verification
```bash
# Generate load and monitor scaling
# Check Cloud Run instances
gcloud run services describe webrtc-platform-dev-signaling \
  --region=us-central1 \
  --format="value(status.traffic[0].revisionName)"
```

## Monitoring and Alerting Tests

### 1. Monitoring Dashboard
```bash
# Get dashboard URL
DASHBOARD_URL=$(cd terraform/environments/dev && terraform output -raw monitoring_dashboard_url)
echo "Monitoring Dashboard: $DASHBOARD_URL"
```

### 2. Alert Testing
```bash
# Trigger test alerts (if configured)
# This would involve generating load or stopping services temporarily
```

## Security Testing

### 1. IAM and Permissions
```bash
# Check service account permissions
gcloud projects get-iam-policy your-project-id \
  --flatten="bindings[].members" \
  --filter="bindings.members:webrtc-platform-dev"
```

### 2. Network Security
```bash
# Verify firewall rules are restrictive
gcloud compute firewall-rules list --filter="name:webrtc-platform-dev" \
  --format="table(name,sourceRanges[],allowed[].ports)"
```

### 3. Secret Management
```bash
# Verify secrets are properly configured
gcloud secrets list --filter="name:webrtc-platform-dev"
```

## Disaster Recovery Testing

### 1. Backup and Restore
```bash
# Test Terraform state backup
cd terraform/environments/dev
cp terraform.tfstate terraform.tfstate.test-backup

# Test infrastructure recreation
terraform destroy -auto-approve
terraform apply -auto-approve
```

### 2. Service Recovery
```bash
# Test service restart
gcloud run services update webrtc-platform-dev-signaling \
  --region=us-central1 \
  --set-env-vars="TEST_VAR=test"

# Test TURN server restart
gcloud compute instances reset webrtc-platform-dev-turn-server \
  --zone=us-central1-a
```

## Cleanup After Testing

### 1. Remove Test Resources
```bash
# Destroy test environment
./scripts/destroy.sh dev
```

### 2. Clean Up Artifacts
```bash
# Remove test Docker images
gcloud artifacts docker images list us-central1-docker.pkg.dev/your-project-id/webrtc-platform-dev-repo
# Delete specific images if needed
```

## Continuous Testing

### 1. Automated Testing Pipeline
```yaml
# Example GitHub Actions workflow
name: Infrastructure Test
on:
  pull_request:
    paths:
      - 'terraform/**'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v1
      - name: Terraform Validate
        run: terraform validate
      - name: Terraform Plan
        run: terraform plan
```

### 2. Health Monitoring
```bash
# Set up continuous monitoring
# This would be configured in the monitoring module
# and would include:
# - Service uptime checks
# - Performance metrics
# - Error rate monitoring
# - Resource utilization alerts
```

## Test Checklist

Before considering the deployment successful, verify:

- [ ] All Terraform resources created successfully
- [ ] Signaling server responds to HTTP requests
- [ ] Media server is accessible and healthy
- [ ] TURN server accepts connections on ports 3478 and 5349
- [ ] WebRTC application loads and functions
- [ ] Room creation and joining works
- [ ] Video/audio streaming works between peers
- [ ] TURN server is used for NAT traversal (check browser console)
- [ ] Monitoring dashboard shows service metrics
- [ ] Logs are flowing to Cloud Logging
- [ ] All firewall rules are properly configured
- [ ] Service accounts have correct permissions
- [ ] Secrets are properly managed
- [ ] Auto-scaling works under load
- [ ] Services recover from failures

## Troubleshooting Common Issues

### Service Not Accessible
1. Check firewall rules
2. Verify service is running
3. Check IAM permissions
4. Review service logs

### TURN Server Issues
1. Verify instance is running
2. Check Docker container status
3. Test port connectivity
4. Review coturn logs

### Performance Issues
1. Check resource limits
2. Monitor CPU/memory usage
3. Review auto-scaling configuration
4. Analyze network latency

### Deployment Failures
1. Check Terraform logs
2. Verify API permissions
3. Check resource quotas
4. Review dependency order
