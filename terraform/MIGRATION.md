# Migration Guide: From Manual Deployment to Terraform

This guide helps you migrate from the current bash script-based deployment to the new Terraform infrastructure management.

## Overview

### Current State (Manual Deployment)
- **Main App**: Deployed via `deploy.sh` to Cloud Run
- **Media Server**: Deployed via `media-server/deploy.sh` with Cloud Build
- **TURN Server**: Deployed via `turn-server/deploy-gce.sh` to Compute Engine
- **Configuration**: Manual environment variable setup

### Target State (Terraform)
- **Infrastructure as Code**: All resources defined in Terraform
- **Environment Management**: Separate dev/staging/prod configurations
- **Automated Deployment**: Single script deploys everything
- **Consistent Configuration**: Environment variables managed centrally

## Pre-Migration Checklist

### 1. Backup Current Deployment
```bash
# Document current service URLs
gcloud run services list --format="table(metadata.name,status.url)"

# Document TURN server IP
gcloud compute instances list --filter="name:coturn-turn-server"

# Backup current environment variables
cp .env .env.backup
```

### 2. Inventory Existing Resources
```bash
# List Cloud Run services
gcloud run services list

# List Compute Engine instances
gcloud compute instances list

# List Artifact Registry repositories
gcloud artifacts repositories list

# List VPC networks
gcloud compute networks list
```

### 3. Prepare Terraform Environment
```bash
# Install Terraform (if not already installed)
brew install terraform  # macOS
# or download from https://terraform.io/downloads

# Verify installation
terraform version
```

## Migration Steps

### Step 1: Prepare Terraform Configuration

1. **Navigate to terraform directory**:
```bash
cd terraform/environments/dev  # or staging/prod
```

2. **Create terraform.tfvars**:
```bash
cp terraform.tfvars.example terraform.tfvars
```

3. **Update terraform.tfvars** with your project details:
```hcl
project_id = "your-actual-project-id"
region     = "us-central1"
environment = "dev"

# Update image URLs to match your current setup
signaling_server_image = "gcr.io/your-project-id/webrtc-signaling:latest"
media_server_image     = "gcr.io/your-project-id/media-server:latest"

# TURN server credentials (use your current ones)
turn_username = "webrtc"
turn_password = "your-current-password"

# Monitoring (optional)
notification_email = "<EMAIL>"
```

### Step 2: Import Existing Resources (Optional)

If you want to preserve existing resources, you can import them into Terraform state:

```bash
# Initialize Terraform
terraform init

# Import existing Cloud Run services (if they exist)
terraform import 'module.webrtc_platform.module.cloud_run.google_cloud_run_service.signaling_server' \
  projects/YOUR_PROJECT_ID/locations/us-central1/services/poc-webrtc

# Import TURN server instance (if it exists)
terraform import 'module.webrtc_platform.module.compute.google_compute_instance.turn_server' \
  projects/YOUR_PROJECT_ID/zones/us-central1-a/instances/coturn-turn-server
```

**Note**: Importing is complex and optional. It's often easier to destroy and recreate.

### Step 3: Deploy with Terraform

#### Option A: Clean Deployment (Recommended)

1. **Destroy existing resources** (if you want a clean start):
```bash
# Stop current services manually or use existing scripts
# This ensures no conflicts with Terraform deployment
```

2. **Deploy with Terraform**:
```bash
cd terraform
./scripts/deploy-all.sh dev your-project-id
```

#### Option B: Gradual Migration

1. **Deploy Terraform infrastructure** alongside existing:
```bash
# Use different resource names to avoid conflicts
# Update terraform.tfvars to use different prefixes
app_name = "webrtc-platform-new"
```

2. **Test the new infrastructure**

3. **Switch traffic** to new services

4. **Destroy old infrastructure**

### Step 4: Update CI/CD and Scripts

1. **Update deployment scripts** to use Terraform:
```bash
# Replace old deploy.sh with
./terraform/scripts/deploy-all.sh dev your-project-id
```

2. **Update environment variables**:
```bash
# The deploy script automatically configures .env
# Verify the configuration:
cat .env
```

3. **Update documentation** and team processes

## Verification Steps

### 1. Test All Services
```bash
# Run connectivity tests
./terraform/scripts/test-connectivity.sh dev

# Test the application manually
# Visit the signaling server URL
# Create a room and test WebRTC functionality
```

### 2. Verify Monitoring
```bash
# Check monitoring dashboard (if enabled)
terraform output monitoring_dashboard_url

# Verify logs are flowing
gcloud logs tail --service=webrtc-platform-dev-signaling
```

### 3. Test Deployment Process
```bash
# Make a small change and redeploy
terraform plan
terraform apply

# Test the automated deployment script
./scripts/deploy-all.sh dev your-project-id
```

## Rollback Plan

If you need to rollback to the manual deployment:

### 1. Preserve Current State
```bash
# Backup Terraform state
cp terraform.tfstate terraform.tfstate.backup

# Document current Terraform-managed URLs
terraform output
```

### 2. Destroy Terraform Resources
```bash
./scripts/destroy.sh dev
```

### 3. Restore Manual Deployment
```bash
# Use your original deployment scripts
./deploy.sh
cd media-server && ./deploy.sh
cd turn-server && ./deploy-gce.sh && ./configure-client.sh
```

## Common Issues and Solutions

### Issue: Resource Already Exists
**Problem**: Terraform tries to create resources that already exist.

**Solution**:
```bash
# Either import the resource or destroy it manually
gcloud run services delete SERVICE_NAME --region=us-central1
```

### Issue: Permission Denied
**Problem**: Service account lacks necessary permissions.

**Solution**:
```bash
# Ensure you're authenticated with sufficient permissions
gcloud auth login
gcloud config set project YOUR_PROJECT_ID

# Check required APIs are enabled
gcloud services list --enabled
```

### Issue: TURN Server Not Accessible
**Problem**: TURN server ports are blocked.

**Solution**:
```bash
# Check firewall rules
gcloud compute firewall-rules list

# Test connectivity
./scripts/test-connectivity.sh dev
```

### Issue: Docker Images Not Found
**Problem**: Terraform references non-existent Docker images.

**Solution**:
```bash
# Build and push images first
cd terraform
./scripts/deploy-all.sh dev your-project-id
# This script builds images before deploying infrastructure
```

## Post-Migration Best Practices

### 1. Environment Management
- Use separate Terraform workspaces for dev/staging/prod
- Never apply prod changes without testing in staging
- Use remote state storage for team collaboration

### 2. Security
- Store sensitive variables in Secret Manager
- Use least-privilege IAM roles
- Regularly rotate TURN server credentials

### 3. Monitoring
- Set up alerts for service health
- Monitor resource usage and costs
- Use structured logging

### 4. Backup and Recovery
- Regular Terraform state backups
- Document disaster recovery procedures
- Test recovery procedures regularly

## Getting Help

If you encounter issues during migration:

1. **Check logs**:
```bash
# Terraform logs
export TF_LOG=DEBUG
terraform apply

# Service logs
gcloud logs tail --service=SERVICE_NAME
```

2. **Validate configuration**:
```bash
terraform validate
terraform plan
```

3. **Test connectivity**:
```bash
./scripts/test-connectivity.sh dev
```

4. **Review documentation**:
- [Terraform README](README.md)
- [Google Cloud Documentation](https://cloud.google.com/docs)
- [WebRTC Platform Documentation](../README.md)
