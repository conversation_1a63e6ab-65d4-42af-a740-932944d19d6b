# Network Outputs
output "vpc_name" {
  description = "Name of the VPC network"
  value       = module.networking.vpc_name
}

output "subnet_name" {
  description = "Name of the subnet"
  value       = module.networking.subnet_name
}

# Cloud Run Service Outputs
output "signaling_server_url" {
  description = "URL of the signaling server"
  value       = module.signaling_server.signaling_server_url
}

output "media_server_url" {
  description = "URL of the media server"
  value       = module.media_server.media_server_url
}

output "media_server_ip" {
  description = "IP address of the media server"
  value       = module.media_server.media_server_ip
}

# TURN Server Outputs
output "turn_server_ip" {
  description = "External IP address of the TURN server"
  value       = module.compute.turn_server_ip
}

output "turn_server_internal_ip" {
  description = "Internal IP address of the TURN server"
  value       = module.compute.turn_server_internal_ip
}

# Artifact Registry Outputs
output "artifact_registry_url" {
  description = "URL of the Artifact Registry repository"
  value       = module.registry.repository_url
}

# Service Account Outputs
output "signaling_service_account_email" {
  description = "Email of the signaling server service account"
  value       = module.security.signaling_service_account_email
}

output "media_service_account_email" {
  description = "Email of the media server service account"
  value       = module.security.media_service_account_email
}

output "turn_service_account_email" {
  description = "Email of the TURN server service account"
  value       = module.security.turn_service_account_email
}

# Monitoring Outputs
output "monitoring_dashboard_url" {
  description = "URL of the monitoring dashboard"
  value       = var.enable_monitoring ? module.monitoring[0].dashboard_url : null
}

# Environment Configuration
output "environment_config" {
  description = "Environment configuration for application deployment"
  value = {
    project_id           = var.project_id
    region              = var.region
    environment         = var.environment
    signaling_server_url = module.signaling_server.signaling_server_url
    media_server_url    = module.media_server.media_server_url
    turn_server_ip      = module.compute.turn_server_ip
    turn_username       = var.turn_username
    artifact_registry   = module.registry.repository_url
  }
  sensitive = true
}

# Deployment Commands
output "deployment_commands" {
  description = "Commands to deploy applications to the infrastructure"
  sensitive   = true
  value = {
    signaling_server = "gcloud run deploy webrtc-signaling --image ${module.registry.repository_url}/webrtc-signaling:latest --region ${var.region}"
    media_server     = "gcloud compute ssh ${module.media_server.instance_name} --zone=${module.media_server.instance_zone} --command='sudo systemctl restart media-server'"
    configure_turn   = "export VITE_TURN_SERVER=${module.compute.turn_server_ip} && export VITE_TURN_USERNAME=${var.turn_username}"
  }
}

# DNS Configuration (if custom domain is used)
output "dns_configuration" {
  description = "DNS configuration for custom domain"
  value = var.domain_name != "" ? {
    domain_name = var.domain_name
    dns_records = [
      {
        name = "@"
        type = "A"
        value = module.signaling_server.load_balancer_ip
      },
      {
        name = "api"
        type = "CNAME"
        value = module.signaling_server.signaling_server_url
      },
      {
        name = "media"
        type = "A"
        value = module.media_server.media_server_ip
      }
    ]
  } : null
}

# Security Information
output "security_info" {
  description = "Security configuration information"
  value = {
    firewall_rules = module.networking.firewall_rules
    service_accounts = {
      signaling = module.security.signaling_service_account_email
      media     = module.security.media_service_account_email
      turn      = module.security.turn_service_account_email
    }
  }
}
