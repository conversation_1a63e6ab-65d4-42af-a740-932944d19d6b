#!/bin/bash

# Destroy Infrastructure Script
# Usage: ./destroy.sh [environment]

set -e

# Configuration
ENVIRONMENT=${1:-dev}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TERRAFORM_DIR="$(dirname "$SCRIPT_DIR")"
ENV_DIR="$TERRAFORM_DIR/environments/$ENVIRONMENT"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validation functions
validate_environment() {
    if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
        log_error "Invalid environment: $ENVIRONMENT. Must be dev, staging, or prod."
        exit 1
    fi
}

confirm_destruction() {
    log_warning "⚠️  WARNING: This will destroy ALL infrastructure for the $ENVIRONMENT environment!"
    log_warning "This action cannot be undone."
    echo ""
    
    if [[ "$ENVIRONMENT" == "prod" ]]; then
        log_error "🚨 PRODUCTION ENVIRONMENT DETECTED!"
        log_error "Destroying production infrastructure requires additional confirmation."
        echo ""
        echo "Type 'DESTROY PRODUCTION' to confirm:"
        read -r confirmation
        if [[ "$confirmation" != "DESTROY PRODUCTION" ]]; then
            log_info "Destruction cancelled."
            exit 0
        fi
    else
        echo "Type 'yes' to confirm destruction of $ENVIRONMENT environment:"
        read -r confirmation
        if [[ "$confirmation" != "yes" ]]; then
            log_info "Destruction cancelled."
            exit 0
        fi
    fi
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        log_error "Terraform is not installed. Please install it first."
        exit 1
    fi
    
    # Check if environment directory exists
    if [[ ! -d "$ENV_DIR" ]]; then
        log_error "Environment directory not found: $ENV_DIR"
        exit 1
    fi
    
    # Check if terraform state exists
    if [[ ! -f "$ENV_DIR/terraform.tfstate" ]] && [[ ! -f "$ENV_DIR/.terraform/terraform.tfstate" ]]; then
        log_warning "No Terraform state found. Infrastructure may not exist."
        echo "Continue anyway? (yes/no):"
        read -r continue_anyway
        if [[ "$continue_anyway" != "yes" ]]; then
            log_info "Destruction cancelled."
            exit 0
        fi
    fi
    
    log_success "Prerequisites check passed"
}

backup_state() {
    log_info "Creating backup of Terraform state..."
    
    cd "$ENV_DIR"
    
    # Create backup directory
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup state files
    if [[ -f "terraform.tfstate" ]]; then
        cp terraform.tfstate "$BACKUP_DIR/"
    fi
    
    if [[ -f "terraform.tfstate.backup" ]]; then
        cp terraform.tfstate.backup "$BACKUP_DIR/"
    fi
    
    # Backup terraform.tfvars
    if [[ -f "terraform.tfvars" ]]; then
        cp terraform.tfvars "$BACKUP_DIR/"
    fi
    
    log_success "State backed up to: $ENV_DIR/$BACKUP_DIR"
}

destroy_infrastructure() {
    log_info "Destroying infrastructure with Terraform..."
    
    cd "$ENV_DIR"
    
    # Initialize Terraform (in case it's not initialized)
    log_info "Initializing Terraform..."
    terraform init
    
    # Plan destruction
    log_info "Planning Terraform destruction..."
    terraform plan -destroy -out=destroy.tfplan
    
    # Show what will be destroyed
    log_warning "The following resources will be destroyed:"
    terraform show destroy.tfplan
    
    echo ""
    log_warning "Last chance to cancel. Proceed with destruction? (yes/no):"
    read -r final_confirmation
    if [[ "$final_confirmation" != "yes" ]]; then
        log_info "Destruction cancelled."
        rm -f destroy.tfplan
        exit 0
    fi
    
    # Apply destruction
    log_info "Applying Terraform destruction..."
    terraform apply destroy.tfplan
    
    # Clean up plan file
    rm -f destroy.tfplan
    
    log_success "Infrastructure destruction completed"
}

cleanup_artifacts() {
    log_info "Cleaning up remaining artifacts..."
    
    # Note: This is a placeholder for additional cleanup
    # In a real scenario, you might want to:
    # - Delete Docker images from Artifact Registry
    # - Clean up any remaining storage buckets
    # - Remove DNS records
    # - Clean up monitoring dashboards
    
    log_info "Manual cleanup may be required for:"
    log_info "- Docker images in Artifact Registry"
    log_info "- Any custom DNS records"
    log_info "- Monitoring dashboards and alerts"
    log_info "- Log entries (if retention is set)"
}

show_destruction_summary() {
    log_info "Destruction Summary:"
    
    echo "=================================="
    echo "Environment: $ENVIRONMENT"
    echo "Status: DESTROYED"
    echo ""
    echo "What was destroyed:"
    echo "- VPC network and subnets"
    echo "- Cloud Run services"
    echo "- Compute Engine instances (TURN server)"
    echo "- Artifact Registry repositories"
    echo "- Service accounts and IAM bindings"
    echo "- Monitoring resources"
    echo "- Firewall rules"
    echo ""
    echo "Backup location: $ENV_DIR/backups/"
    echo ""
    echo "To redeploy:"
    echo "./deploy-all.sh $ENVIRONMENT [project_id]"
    echo "=================================="
}

# Main execution
main() {
    log_info "Starting destruction for environment: $ENVIRONMENT"
    
    validate_environment
    confirm_destruction
    check_prerequisites
    backup_state
    destroy_infrastructure
    cleanup_artifacts
    show_destruction_summary
    
    log_success "Destruction completed successfully!"
}

# Run main function
main "$@"
