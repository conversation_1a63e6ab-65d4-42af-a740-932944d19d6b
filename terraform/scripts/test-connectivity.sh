#!/bin/bash

# Test Connectivity Script
# Usage: ./test-connectivity.sh [environment]

set -e

# Configuration
ENVIRONMENT=${1:-dev}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TERRAFORM_DIR="$(dirname "$SCRIPT_DIR")"
ENV_DIR="$TERRAFORM_DIR/environments/$ENVIRONMENT"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test functions
test_service_url() {
    local service_name=$1
    local url=$2
    
    log_info "Testing $service_name at $url"
    
    if curl -s --max-time 10 "$url" > /dev/null; then
        log_success "$service_name is accessible"
        return 0
    else
        log_error "$service_name is not accessible"
        return 1
    fi
}

test_turn_server() {
    local turn_ip=$1
    local turn_port=${2:-3478}
    
    log_info "Testing TURN server at $turn_ip:$turn_port"
    
    # Test TCP connectivity
    if timeout 5 bash -c "</dev/tcp/$turn_ip/$turn_port" 2>/dev/null; then
        log_success "TURN server TCP port $turn_port is accessible"
    else
        log_error "TURN server TCP port $turn_port is not accessible"
        return 1
    fi
    
    # Test UDP connectivity (basic check)
    if command -v nc &> /dev/null; then
        if timeout 5 nc -u -z "$turn_ip" "$turn_port" 2>/dev/null; then
            log_success "TURN server UDP port $turn_port appears accessible"
        else
            log_warning "TURN server UDP port $turn_port may not be accessible (nc test)"
        fi
    else
        log_warning "netcat not available, skipping UDP test"
    fi
    
    return 0
}

test_gce_instance() {
    local instance_name=$1
    local zone=$2
    local project_id=$3
    
    log_info "Testing GCE instance: $instance_name"
    
    # Check instance status
    local status=$(gcloud compute instances describe "$instance_name" \
        --zone="$zone" \
        --project="$project_id" \
        --format="value(status)" 2>/dev/null || echo "NOT_FOUND")
    
    if [[ "$status" == "RUNNING" ]]; then
        log_success "GCE instance $instance_name is running"
        return 0
    elif [[ "$status" == "NOT_FOUND" ]]; then
        log_error "GCE instance $instance_name not found"
        return 1
    else
        log_error "GCE instance $instance_name status: $status"
        return 1
    fi
}

test_cloud_run_service() {
    local service_name=$1
    local region=$2
    local project_id=$3
    
    log_info "Testing Cloud Run service: $service_name"
    
    # Check service status
    local status=$(gcloud run services describe "$service_name" \
        --region="$region" \
        --project="$project_id" \
        --format="value(status.conditions[0].status)" 2>/dev/null || echo "NOT_FOUND")
    
    if [[ "$status" == "True" ]]; then
        log_success "Cloud Run service $service_name is ready"
        return 0
    elif [[ "$status" == "NOT_FOUND" ]]; then
        log_error "Cloud Run service $service_name not found"
        return 1
    else
        log_error "Cloud Run service $service_name status: $status"
        return 1
    fi
}

get_terraform_outputs() {
    cd "$ENV_DIR"
    
    if [[ ! -f "terraform.tfstate" ]]; then
        log_error "No Terraform state found in $ENV_DIR"
        exit 1
    fi
    
    # Get outputs
    SIGNALING_URL=$(terraform output -raw signaling_server_url 2>/dev/null || echo "")
    MEDIA_URL=$(terraform output -raw media_server_url 2>/dev/null || echo "")
    TURN_IP=$(terraform output -raw turn_server_ip 2>/dev/null || echo "")
    PROJECT_ID=$(terraform output -raw environment_config 2>/dev/null | jq -r '.project_id' 2>/dev/null || echo "")
    REGION=$(terraform output -raw environment_config 2>/dev/null | jq -r '.region' 2>/dev/null || echo "us-central1")
}

run_connectivity_tests() {
    log_info "Running connectivity tests for $ENVIRONMENT environment..."
    
    local test_results=()
    
    # Test signaling server
    if [[ -n "$SIGNALING_URL" ]]; then
        if test_service_url "Signaling Server" "$SIGNALING_URL"; then
            test_results+=("✅ Signaling Server")
        else
            test_results+=("❌ Signaling Server")
        fi
    else
        test_results+=("⚠️  Signaling Server (URL not found)")
    fi
    
    # Test media server
    if [[ -n "$MEDIA_URL" ]]; then
        if test_service_url "Media Server" "$MEDIA_URL"; then
            test_results+=("✅ Media Server")
        else
            test_results+=("❌ Media Server")
        fi
    else
        test_results+=("⚠️  Media Server (URL not found)")
    fi
    
    # Test TURN server
    if [[ -n "$TURN_IP" ]]; then
        if test_turn_server "$TURN_IP"; then
            test_results+=("✅ TURN Server")
        else
            test_results+=("❌ TURN Server")
        fi
    else
        test_results+=("⚠️  TURN Server (IP not found)")
    fi
    
    # Test Cloud Run services
    if [[ -n "$PROJECT_ID" ]]; then
        if test_cloud_run_service "webrtc-platform-$ENVIRONMENT-signaling" "$REGION" "$PROJECT_ID"; then
            test_results+=("✅ Signaling Service Status")
        else
            test_results+=("❌ Signaling Service Status")
        fi
        
        if test_cloud_run_service "webrtc-platform-$ENVIRONMENT-media" "$REGION" "$PROJECT_ID"; then
            test_results+=("✅ Media Service Status")
        else
            test_results+=("❌ Media Service Status")
        fi
        
        if test_gce_instance "webrtc-platform-$ENVIRONMENT-turn-server" "us-central1-a" "$PROJECT_ID"; then
            test_results+=("✅ TURN Instance Status")
        else
            test_results+=("❌ TURN Instance Status")
        fi
    fi
    
    # Display results
    echo ""
    log_info "Test Results Summary:"
    echo "=================================="
    for result in "${test_results[@]}"; do
        echo "$result"
    done
    echo "=================================="
}

show_service_info() {
    log_info "Service Information:"
    echo "=================================="
    echo "Environment: $ENVIRONMENT"
    echo "Project ID: $PROJECT_ID"
    echo "Region: $REGION"
    echo ""
    echo "Service URLs:"
    echo "  Signaling Server: ${SIGNALING_URL:-'Not available'}"
    echo "  Media Server: ${MEDIA_URL:-'Not available'}"
    echo "  TURN Server: ${TURN_IP:-'Not available'}:3478"
    echo ""
    echo "Useful Commands:"
    echo "  View logs: gcloud logs tail --service=webrtc-platform-$ENVIRONMENT-signaling"
    echo "  SSH to TURN: gcloud compute ssh webrtc-platform-$ENVIRONMENT-turn-server --zone=us-central1-a"
    echo "  Check services: gcloud run services list --region=$REGION"
    echo "=================================="
}

# Main execution
main() {
    log_info "Testing connectivity for environment: $ENVIRONMENT"
    
    # Validate environment
    if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
        log_error "Invalid environment: $ENVIRONMENT. Must be dev, staging, or prod."
        exit 1
    fi
    
    # Check if environment directory exists
    if [[ ! -d "$ENV_DIR" ]]; then
        log_error "Environment directory not found: $ENV_DIR"
        exit 1
    fi
    
    get_terraform_outputs
    run_connectivity_tests
    show_service_info
    
    log_success "Connectivity test completed!"
}

# Run main function
main "$@"
