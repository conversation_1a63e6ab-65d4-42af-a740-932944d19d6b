#!/bin/bash

# Deploy All Services Script
# Usage: ./deploy-all.sh [environment] [project_id]

set -e

# Configuration
ENVIRONMENT=${1:-dev}
PROJECT_ID=${2:-}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TERRAFORM_DIR="$(dirname "$SCRIPT_DIR")"
ENV_DIR="$TERRAFORM_DIR/environments/$ENVIRONMENT"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validation functions
validate_environment() {
    if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
        log_error "Invalid environment: $ENVIRONMENT. Must be dev, staging, or prod."
        exit 1
    fi
}

validate_project_id() {
    if [[ -z "$PROJECT_ID" ]]; then
        log_error "Project ID is required. Usage: $0 [environment] [project_id]"
        exit 1
    fi
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if gcloud is installed and authenticated
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Check if terraform is installed
    if ! command -v terraform &> /dev/null; then
        log_error "Terraform is not installed. Please install it first."
        exit 1
    fi
    
    # Check if docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install it first."
        exit 1
    fi
    
    # Check if authenticated with gcloud
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
        log_error "Not authenticated with gcloud. Please run 'gcloud auth login'."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

setup_project() {
    log_info "Setting up Google Cloud project..."
    
    gcloud config set project "$PROJECT_ID"
    
    # Enable required APIs
    log_info "Enabling required APIs..."
    gcloud services enable \
        compute.googleapis.com \
        run.googleapis.com \
        artifactregistry.googleapis.com \
        cloudbuild.googleapis.com \
        logging.googleapis.com \
        monitoring.googleapis.com \
        secretmanager.googleapis.com \
        dns.googleapis.com \
        vpcaccess.googleapis.com
    
    log_success "Project setup completed"
}

deploy_infrastructure_base() {
    log_info "Deploying base infrastructure (networking, registry, security)..."

    cd "$ENV_DIR"

    # Check if terraform.tfvars exists
    if [[ ! -f "terraform.tfvars" ]]; then
        log_warning "terraform.tfvars not found. Creating from example..."
        cp terraform.tfvars.example terraform.tfvars

        # Update project_id in terraform.tfvars
        sed -i.bak "s/your-gcp-project-id/$PROJECT_ID/g" terraform.tfvars
        sed -i.bak "s/your-project-id/$PROJECT_ID/g" terraform.tfvars
        rm terraform.tfvars.bak

        log_warning "Please review and update terraform.tfvars with your specific values"
        log_warning "Press Enter to continue or Ctrl+C to abort..."
        read -r
    fi

    # Initialize Terraform
    log_info "Initializing Terraform..."
    terraform init

    # Deploy only the base infrastructure first (networking, registry, security)
    log_info "Planning base infrastructure deployment..."
    terraform plan -target=module.webrtc_platform.module.networking \
                   -target=module.webrtc_platform.module.registry \
                   -target=module.webrtc_platform.module.security \
                   -target=google_project_service.required_apis \
                   -out=base-tfplan

    log_info "Applying base infrastructure deployment..."
    terraform apply base-tfplan

    log_success "Base infrastructure deployment completed"
}

build_and_push_images() {
    log_info "Building and pushing Docker images..."

    # Navigate to project root
    cd "$TERRAFORM_DIR/.."

    # Get the artifact registry URL from terraform output
    cd "$ENV_DIR"

    # Try to get the registry URL, but handle the case where outputs aren't ready
    REGISTRY_URL=$(terraform output -raw artifact_registry_url 2>/dev/null | grep -v "Warning" | grep -v "│" | grep -v "╷" | grep -v "╵" | head -1)

    # If we can't get it from output, construct it from known values
    if [[ -z "$REGISTRY_URL" || "$REGISTRY_URL" == *"Warning"* || "$REGISTRY_URL" == *"No outputs"* ]]; then
        log_info "Constructing registry URL from project configuration..."
        REGISTRY_URL="us-central1-docker.pkg.dev/$PROJECT_ID/webrtc-platform-$ENVIRONMENT-repo"
        log_info "Using constructed registry URL: $REGISTRY_URL"
    else
        log_info "Using registry URL from Terraform output: $REGISTRY_URL"
    fi

    # Navigate back to project root
    cd "$TERRAFORM_DIR/.."

    # Configure Docker authentication
    gcloud auth configure-docker us-central1-docker.pkg.dev --quiet

    # Build and push signaling server
    log_info "Building signaling server image for linux/amd64..."
    docker build --platform linux/amd64 -t "$REGISTRY_URL/webrtc-signaling:latest" .
    docker push "$REGISTRY_URL/webrtc-signaling:latest"

    # Build and push media server
    log_info "Building media server image for linux/amd64..."
    cd media-server
    docker build --platform linux/amd64 -t "$REGISTRY_URL/media-server:latest" .
    docker push "$REGISTRY_URL/media-server:latest"

    cd ..
    log_success "Docker images built and pushed"
}

deploy_infrastructure_complete() {
    log_info "Deploying complete infrastructure with Terraform..."

    cd "$ENV_DIR"

    # Now deploy the complete infrastructure including Cloud Run services
    log_info "Planning complete infrastructure deployment..."
    terraform plan -out=complete-tfplan

    # Apply complete deployment
    log_info "Applying complete infrastructure deployment..."
    terraform apply complete-tfplan

    log_success "Complete infrastructure deployment finished"
}

update_service_urls() {
    log_info "Updating service URLs after deployment..."

    cd "$ENV_DIR"

    # Get the actual media server URL
    MEDIA_SERVER_URL=$(terraform output -raw media_server_url 2>/dev/null || echo "")

    if [[ -n "$MEDIA_SERVER_URL" ]]; then
        log_info "Updating signaling server with media server URL: $MEDIA_SERVER_URL"

        # Update the signaling server environment variable
        gcloud run services update "webrtc-platform-$ENVIRONMENT-signaling" \
            --region=us-central1 \
            --set-env-vars="MEDIA_SERVER_URL=$MEDIA_SERVER_URL" \
            --project="$PROJECT_ID"

        log_success "Service URLs updated"
    else
        log_warning "Could not get media server URL from Terraform output"
    fi
}

deploy_applications() {
    log_info "Applications are deployed via Terraform Cloud Run services"
    log_info "Docker images have been built and pushed to Artifact Registry"
    log_success "Applications deployed via Terraform"
}

configure_environment() {
    log_info "Configuring environment variables..."

    cd "$ENV_DIR"

    # Get service URLs from Terraform output
    TURN_SERVER_IP=$(terraform output -raw turn_server_ip 2>/dev/null || echo "")
    SIGNALING_URL=$(terraform output -raw signaling_server_url 2>/dev/null || echo "")
    MEDIA_URL=$(terraform output -raw media_server_url 2>/dev/null || echo "")

    # Update .env file in project root
    cd "$TERRAFORM_DIR/.."

    # Create or update .env file
    if [[ -f .env ]]; then
        # Remove existing configuration
        sed -i.bak '/^VITE_TURN_/d; /^VITE_MEDIA_SERVER_URL/d' .env
    else
        touch .env
    fi

    # Add service configuration
    if [[ -n "$TURN_SERVER_IP" ]]; then
        echo "VITE_TURN_SERVER=$TURN_SERVER_IP" >> .env
        echo "VITE_TURN_USERNAME=webrtc" >> .env
        echo "VITE_TURN_PASSWORD=webrtc123" >> .env
        log_success "Environment configured with TURN server: $TURN_SERVER_IP"
    fi

    if [[ -n "$MEDIA_URL" ]]; then
        echo "VITE_MEDIA_SERVER_URL=$MEDIA_URL" >> .env
        log_success "Environment configured with media server: $MEDIA_URL"
    fi

    log_info "Updated .env file with deployed service URLs"

    # Rebuild frontend with new environment variables
    log_info "Rebuilding frontend with updated environment variables..."
    npm run build

    # Rebuild and push signaling server with updated frontend
    log_info "Rebuilding signaling server with updated frontend..."
    REGISTRY_URL="us-central1-docker.pkg.dev/$PROJECT_ID/webrtc-platform-$ENVIRONMENT-repo"
    docker build --platform linux/amd64 -t "$REGISTRY_URL/webrtc-signaling:latest" .
    docker push "$REGISTRY_URL/webrtc-signaling:latest"

    # Update the Cloud Run service to use the new image
    log_info "Updating signaling server deployment..."
    gcloud run services update "webrtc-platform-$ENVIRONMENT-signaling" \
        --region=us-central1 \
        --image="$REGISTRY_URL/webrtc-signaling:latest" \
        --project="$PROJECT_ID"

    log_success "Frontend rebuilt and deployed with cloud service URLs"
}

show_deployment_info() {
    log_info "Deployment Summary:"
    
    cd "$ENV_DIR"
    
    echo "=================================="
    echo "Environment: $ENVIRONMENT"
    echo "Project ID: $PROJECT_ID"
    echo ""
    
    # Show service URLs
    SIGNALING_URL=$(terraform output -raw signaling_server_url 2>/dev/null || echo "Not available")
    MEDIA_URL=$(terraform output -raw media_server_url 2>/dev/null || echo "Not available")
    TURN_IP=$(terraform output -raw turn_server_ip 2>/dev/null || echo "Not available")
    
    echo "Service URLs:"
    echo "  Signaling Server: $SIGNALING_URL"
    echo "  Media Server: $MEDIA_URL"
    echo "  TURN Server: $TURN_IP:3478"
    echo ""
    
    # Show monitoring dashboard if available
    DASHBOARD_URL=$(terraform output -raw monitoring_dashboard_url 2>/dev/null || echo "")
    if [[ -n "$DASHBOARD_URL" ]]; then
        echo "Monitoring Dashboard: $DASHBOARD_URL"
        echo ""
    fi
    
    echo "Next Steps:"
    echo "1. Test the deployment by visiting the signaling server URL"
    echo "2. Check the monitoring dashboard for service health"
    echo "3. Review logs: gcloud logs tail --service=webrtc-platform-$ENVIRONMENT-signaling"
    echo "=================================="
}

# Main execution
main() {
    log_info "Starting deployment for environment: $ENVIRONMENT"

    validate_environment
    validate_project_id
    check_prerequisites
    setup_project
    deploy_infrastructure_base
    build_and_push_images
    deploy_infrastructure_complete
    update_service_urls
    deploy_applications
    configure_environment
    show_deployment_info

    log_success "Deployment completed successfully!"
}

# Run main function
main "$@"
