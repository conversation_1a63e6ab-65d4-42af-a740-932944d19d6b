{"version": 4, "terraform_version": "1.12.2", "serial": 204, "lineage": "857bdcf3-f01d-2560-9f6e-b67344701c42", "outputs": {"deployment_commands": {"value": {"configure_turn": "export VITE_TURN_SERVER=************** && export VITE_TURN_USERNAME=webrtc", "media_server": "gcloud container clusters get-credentials webrtc-platform-dev-media-cluster --region us-central1 --project switcher-studio-233517", "signaling_server": "gcloud run deploy webrtc-signaling --image us-central1-docker.pkg.dev/switcher-studio-233517/webrtc-platform-dev-repo/webrtc-signaling:latest --region us-central1"}, "type": ["object", {"configure_turn": "string", "media_server": "string", "signaling_server": "string"}], "sensitive": true}, "environment_config": {"value": {"artifact_registry": "us-central1-docker.pkg.dev/switcher-studio-233517/webrtc-platform-dev-repo", "environment": "dev", "media_server_url": "https://************:8080", "project_id": "switcher-studio-233517", "region": "us-central1", "signaling_server_url": "https://webrtc-platform-dev-signaling-7egrqrsfva-uc.a.run.app", "turn_server_ip": "**************", "turn_username": "webrtc"}, "type": ["object", {"artifact_registry": "string", "environment": "string", "media_server_url": "string", "project_id": "string", "region": "string", "signaling_server_url": "string", "turn_server_ip": "string", "turn_username": "string"}], "sensitive": true}, "media_server_url": {"value": "https://************:8080", "type": "string"}, "signaling_server_url": {"value": "https://webrtc-platform-dev-signaling-7egrqrsfva-uc.a.run.app", "type": "string"}, "turn_server_ip": {"value": "**************", "type": "string"}}, "resources": [{"mode": "data", "type": "google_client_config", "name": "default", "provider": "provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"access_token": "**************************************************************************************************************************************************************************************************************************************************************", "default_labels": null, "id": "projects/\"switcher-studio-233517\"/regions/\"us-central1\"/zones/\"us-central1-a\"", "project": "switcher-studio-233517", "region": "us-central1", "zone": "us-central1-a"}, "sensitive_attributes": [[{"type": "get_attr", "value": "access_token"}]], "identity_schema_version": 0}]}, {"module": "module.webrtc_platform", "mode": "managed", "type": "google_project_service", "name": "required_apis", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "artifactregistry.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/artifactregistry.googleapis.com", "project": "switcher-studio-233517", "service": "artifactregistry.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************"}, {"index_key": "cloudbuild.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/cloudbuild.googleapis.com", "project": "switcher-studio-233517", "service": "cloudbuild.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************"}, {"index_key": "compute.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/compute.googleapis.com", "project": "switcher-studio-233517", "service": "compute.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************"}, {"index_key": "dns.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/dns.googleapis.com", "project": "switcher-studio-233517", "service": "dns.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************"}, {"index_key": "logging.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/logging.googleapis.com", "project": "switcher-studio-233517", "service": "logging.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************"}, {"index_key": "monitoring.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/monitoring.googleapis.com", "project": "switcher-studio-233517", "service": "monitoring.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************"}, {"index_key": "run.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/run.googleapis.com", "project": "switcher-studio-233517", "service": "run.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************"}, {"index_key": "secretmanager.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/secretmanager.googleapis.com", "project": "switcher-studio-233517", "service": "secretmanager.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************"}, {"index_key": "vpcaccess.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/vpcaccess.googleapis.com", "project": "switcher-studio-233517", "service": "vpcaccess.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.webrtc_platform.module.compute", "mode": "managed", "type": "google_compute_address", "name": "turn_server_ip", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"address": "**************", "address_type": "EXTERNAL", "creation_timestamp": "2025-08-19T08:01:22.516-07:00", "description": "", "effective_labels": {}, "id": "projects/switcher-studio-233517/regions/us-central1/addresses/webrtc-platform-dev-turn-ip", "ip_version": "", "ipv6_endpoint_type": "", "label_fingerprint": "42WmSpB8rSM=", "labels": {}, "name": "webrtc-platform-dev-turn-ip", "network": "", "network_tier": "PREMIUM", "prefix_length": 0, "project": "switcher-studio-233517", "purpose": "", "region": "us-central1", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/regions/us-central1/addresses/webrtc-platform-dev-turn-ip", "subnetwork": "", "terraform_labels": {}, "timeouts": null, "users": ["https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/instances/webrtc-platform-dev-turn-server"]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.compute", "mode": "managed", "type": "google_compute_health_check", "name": "turn_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"check_interval_sec": 30, "creation_timestamp": "2025-08-19T08:01:21.780-07:00", "description": "", "grpc_health_check": [], "healthy_threshold": 2, "http2_health_check": [], "http_health_check": [], "https_health_check": [], "id": "projects/switcher-studio-233517/global/healthChecks/webrtc-platform-dev-turn-health-check", "log_config": [{"enable": false}], "name": "webrtc-platform-dev-turn-health-check", "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/healthChecks/webrtc-platform-dev-turn-health-check", "source_regions": [], "ssl_health_check": [], "tcp_health_check": [{"port": 3478, "port_name": "", "port_specification": "", "proxy_header": "NONE", "request": "", "response": ""}], "timeout_sec": 10, "timeouts": null, "type": "TCP", "unhealthy_threshold": 2}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"]}]}, {"module": "module.webrtc_platform.module.compute", "mode": "managed", "type": "google_compute_instance", "name": "turn_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 6, "attributes": {"advanced_machine_features": [], "allow_stopping_for_update": null, "attached_disk": [], "boot_disk": [{"auto_delete": true, "device_name": "persistent-disk-0", "disk_encryption_key_raw": "", "disk_encryption_key_sha256": "", "initialize_params": [{"enable_confidential_compute": false, "image": "https://www.googleapis.com/compute/v1/projects/cos-cloud/global/images/cos-stable-121-18867-199-38", "labels": {}, "provisioned_iops": 0, "provisioned_throughput": 0, "resource_manager_tags": {}, "size": 20, "storage_pool": "", "type": "pd-standard"}], "kms_key_self_link": "", "mode": "READ_WRITE", "source": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/disks/webrtc-platform-dev-turn-server"}], "can_ip_forward": false, "confidential_instance_config": [], "cpu_platform": "Intel Broadwell", "current_status": "RUNNING", "deletion_protection": false, "description": "", "desired_status": null, "effective_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "enable_display": false, "guest_accelerator": [], "hostname": "", "id": "projects/switcher-studio-233517/zones/us-central1-a/instances/webrtc-platform-dev-turn-server", "instance_id": "3649050796655980327", "label_fingerprint": "y1DYWkiuggQ=", "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "machine_type": "e2-micro", "metadata": {"enable-oslogin": "TRUE", "startup-script": "#!/bin/bash\n\n# Startup script for TURN server instance\nset -e\n\n# Configure logging\nexec > >(tee /var/log/startup-script.log)\nexec 2>&1\n\necho \"Starting TURN server setup...\"\n\n# Install required packages\napt-get update\napt-get install -y docker.io\n\n# Start Docker service\nsystemctl start docker\nsystemctl enable docker\n\n# Configure Docker authentication for Artifact Registry\ngcloud auth configure-docker --quiet\n\n# Get TURN password from Secret Manager\nTURN_PASSWORD=$(gcloud secrets versions access latest --secret=\"webrtc-platform-dev-turn-password\" --project=\"switcher-studio-233517\")\n\n# Stop any existing coturn container\ndocker stop coturn-server 2>/dev/null || true\ndocker rm coturn-server 2>/dev/null || true\n\n# Create coturn configuration directory\nmkdir -p /etc/coturn\n\n# Create coturn configuration file\ncat > /etc/coturn/turnserver.conf << EOF\n# coturn TURN server configuration for WebRTC\n\n# Listening port for TURN/STUN\nlistening-port=3478\n\n# TLS listening port (optional, for secure connections)\ntls-listening-port=5349\n\n# Relay ports range (for media)\nmin-port=49152\nmax-port=49252\n\n# Enable verbose logging\nverbose\n\n# Log file (will go to stdout in Docker)\nlog-file=stdout\n\n# Realm for authentication\nrealm=webrtc.local\n\n# Server name\nserver-name=turn-server\n\n# Authentication\n# Use long-term credentials\nlt-cred-mech\n\n# Static user credentials\nuser=webrtc:$TURN_PASSWORD\n\n# Allow loopback peers (for local testing)\nallow-loopback-peers\n\n# Disable RFC5780 support (can cause issues)\nno-rfc5780\n\n# Enable STUN\nstun-only=false\n\n# Disable software attributes\nno-software-attribute\nEOF\n\n# Pull and run coturn container\necho \"Pulling TURN server image...\"\ndocker pull coturn/coturn:latest\n\necho \"Starting TURN server container...\"\ndocker run -d \\\n    --name coturn-server \\\n    --restart unless-stopped \\\n    --network host \\\n    -v /etc/coturn/turnserver.conf:/etc/coturn/turnserver.conf:ro \\\n    -e TURN_USERNAME=webrtc \\\n    -e TURN_PASSWORD=$TURN_PASSWORD \\\n    coturn/coturn:latest \\\n    turnserver -c /etc/coturn/turnserver.conf -v\n\necho \"TURN server setup completed successfully\"\n\n# Verify the container is running\nsleep 5\nif docker ps | grep -q coturn-server; then\n    echo \"TURN server is running successfully\"\nelse\n    echo \"ERROR: TURN server failed to start\"\n    docker logs coturn-server\n    exit 1\nfi\n"}, "metadata_fingerprint": "pOHUn3CuF-k=", "metadata_startup_script": null, "min_cpu_platform": "", "name": "webrtc-platform-dev-turn-server", "network_interface": [{"access_config": [{"nat_ip": "**************", "network_tier": "PREMIUM", "public_ptr_domain_name": ""}], "alias_ip_range": [], "internal_ipv6_prefix_length": 0, "ipv6_access_config": [], "ipv6_access_type": "", "ipv6_address": "", "name": "nic0", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "network_ip": "********", "nic_type": "", "queue_count": 0, "stack_type": "IPV4_ONLY", "subnetwork": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/regions/us-central1/subnetworks/webrtc-platform-dev-subnet", "subnetwork_project": "switcher-studio-233517"}], "network_performance_config": [], "params": [], "project": "switcher-studio-233517", "reservation_affinity": [], "resource_policies": [], "scheduling": [{"automatic_restart": true, "instance_termination_action": "", "local_ssd_recovery_timeout": [], "max_run_duration": [], "min_node_cpus": 0, "node_affinities": [], "on_host_maintenance": "MIGRATE", "on_instance_stop_action": [], "preemptible": false, "provisioning_model": "STANDARD"}], "scratch_disk": [], "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/instances/webrtc-platform-dev-turn-server", "service_account": [{"email": "<EMAIL>", "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}], "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false, "enable_vtpm": true}], "tags": ["ssh-server", "turn-server"], "tags_fingerprint": "FH5JVhyFy0A=", "terraform_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "timeouts": null, "zone": "us-central1-a"}, "sensitive_attributes": [[{"type": "get_attr", "value": "boot_disk"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "disk_encryption_key_raw"}], [{"type": "get_attr", "value": "metadata"}, {"type": "index", "value": {"value": "startup-script", "type": "string"}}]], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.compute.google_compute_address.turn_server_ip", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.media_server", "mode": "managed", "type": "google_compute_address", "name": "media_server_ip", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"address": "************", "address_type": "EXTERNAL", "creation_timestamp": "2025-08-20T09:45:51.113-07:00", "description": "", "effective_labels": {}, "id": "projects/switcher-studio-233517/regions/us-central1/addresses/webrtc-platform-dev-media-ip", "ip_version": "", "ipv6_endpoint_type": "", "label_fingerprint": "42WmSpB8rSM=", "labels": {}, "name": "webrtc-platform-dev-media-ip", "network": "", "network_tier": "PREMIUM", "prefix_length": 0, "project": "switcher-studio-233517", "purpose": "", "region": "us-central1", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/regions/us-central1/addresses/webrtc-platform-dev-media-ip", "subnetwork": "", "terraform_labels": {}, "timeouts": null, "users": ["https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/instances/webrtc-platform-dev-media-server"]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.registry.data.google_project.project", "module.webrtc_platform.module.registry.google_artifact_registry_repository.main", "module.webrtc_platform.module.registry.google_artifact_registry_repository_iam_member.cloudbuild_writer", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.media_server", "mode": "managed", "type": "google_compute_instance", "name": "media_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 6, "attributes": {"advanced_machine_features": [], "allow_stopping_for_update": null, "attached_disk": [], "boot_disk": [{"auto_delete": true, "device_name": "persistent-disk-0", "disk_encryption_key_raw": "", "disk_encryption_key_sha256": "", "initialize_params": [{"enable_confidential_compute": false, "image": "https://www.googleapis.com/compute/v1/projects/ubuntu-os-cloud/global/images/ubuntu-2204-jammy-v20250815", "labels": {}, "provisioned_iops": 0, "provisioned_throughput": 0, "resource_manager_tags": {}, "size": 30, "storage_pool": "", "type": "pd-standard"}], "kms_key_self_link": "", "mode": "READ_WRITE", "source": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/disks/webrtc-platform-dev-media-server"}], "can_ip_forward": false, "confidential_instance_config": [], "cpu_platform": "Intel Broadwell", "current_status": "RUNNING", "deletion_protection": false, "description": "", "desired_status": null, "effective_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "enable_display": false, "guest_accelerator": [], "hostname": "", "id": "projects/switcher-studio-233517/zones/us-central1-a/instances/webrtc-platform-dev-media-server", "instance_id": "3309190442545489075", "label_fingerprint": "y1DYWkiuggQ=", "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "machine_type": "e2-standard-2", "metadata": {"enable-oslogin": "TRUE", "startup-script": "#!/bin/bash\n\n# Startup script for media server instance - Native installation\nset -e\n\n# Configure logging\nexec > >(tee /var/log/startup-script.log)\nexec 2>&1\n\necho \"Starting native media server setup...\"\n\n# Update package lists and install required packages\napt-get update\napt-get install -y curl wget gnupg2 software-properties-common openssl\n\n# Install Node.js 18\ncurl -fsSL https://deb.nodesource.com/setup_18.x | bash -\napt-get install -y nodejs\n\n# Verify installation\necho \"Node.js version: $(node --version)\"\necho \"NPM version: $(npm --version)\"\n\n# Create media server directory\nmkdir -p /opt/media-server\ncd /opt/media-server\n\n# Create SSL certificates directory\nmkdir -p /opt/media-server/ssl\n\n# Generate self-signed SSL certificate for HTTPS\nopenssl req -x509 -newkey rsa:4096 -keyout /opt/media-server/ssl/key.pem -out /opt/media-server/ssl/cert.pem -days 365 -nodes -subj \"/C=US/ST=CA/L=SF/O=WebRTC/CN=************\"\n\n# Set proper permissions for SSL certificates\nchmod 644 /opt/media-server/ssl/cert.pem\nchmod 600 /opt/media-server/ssl/key.pem\n\n# Download and extract media server code\necho \"Downloading media server code...\"\n# We'll create the media server files directly since we can't easily extract from Docker image\n\n# Create package.json\ncat > package.json << 'EOF'\n{\n  \"name\": \"media-server\",\n  \"version\": \"1.0.0\",\n  \"description\": \"WebRTC Media Server\",\n  \"main\": \"server.js\",\n  \"scripts\": {\n    \"start\": \"node server.js\"\n  },\n  \"dependencies\": {\n    \"express\": \"^4.18.2\",\n    \"socket.io\": \"^4.7.2\",\n    \"cors\": \"^2.8.5\",\n    \"mediasoup\": \"^3.12.16\",\n    \"ffmpeg-static\": \"^5.2.0\"\n  }\n}\nEOF\n\n# Install dependencies\necho \"Installing Node.js dependencies...\"\nnpm install\n\n# Create the media server with Socket.IO support\ncat > server.js << 'EOF'\nconst express = require('express');\nconst http = require('http');\nconst https = require('https');\nconst fs = require('fs');\nconst socketIo = require('socket.io');\nconst cors = require('cors');\n\nconst app = express();\napp.use(cors());\napp.use(express.json());\n\n// Create server (HTTP or HTTPS based on environment)\nlet server;\nif (process.env.ENABLE_HTTPS === 'true' && process.env.SSL_CERT_PATH && process.env.SSL_KEY_PATH) {\n  console.log('🔒 Starting HTTPS server');\n  const options = {\n    cert: fs.readFileSync(process.env.SSL_CERT_PATH),\n    key: fs.readFileSync(process.env.SSL_KEY_PATH)\n  };\n  server = https.createServer(options, app);\n} else {\n  console.log('🌐 Starting HTTP server');\n  server = http.createServer(app);\n}\n\n// Initialize Socket.IO\nconst io = socketIo(server, {\n  cors: {\n    origin: \"*\",\n    methods: [\"GET\", \"POST\"],\n    credentials: true\n  }\n});\n\n// Health check endpoint\napp.get('/', (req, res) => {\n  res.json({\n    status: 'ok',\n    service: 'media-server-native',\n    timestamp: new Date().toISOString(),\n    connections: io.engine.clientsCount\n  });\n});\n\n// Configuration endpoint for frontend\napp.get('/api/config', (req, res) => {\n  res.json({\n    mediaServerUrl: process.env.MEDIA_SERVER_URL || 'http://localhost:8081',\n    turnServer: process.env.VITE_TURN_SERVER,\n    turnUsername: process.env.VITE_TURN_USERNAME,\n    turnPassword: process.env.VITE_TURN_PASSWORD\n  });\n});\n\n// Socket.IO connection handling\nio.on('connection', (socket) => {\n  console.log('🔌 Client connected:', socket.id);\n  console.log('📊 Total connections:', io.engine.clientsCount);\n\n  // Basic test response\n  socket.on('get-transport-info', () => {\n    console.log('📡 Client requested transport info');\n    socket.emit('transport-info', {\n      message: 'Media server connected successfully',\n      timestamp: new Date().toISOString()\n    });\n  });\n\n  socket.on('disconnect', (reason) => {\n    console.log('🔌 Client disconnected:', socket.id, 'Reason:', reason);\n    console.log('📊 Total connections remaining:', io.engine.clientsCount);\n  });\n});\n\nconst PORT = process.env.PORT || 8080;\nconst HOST = process.env.LISTEN_IP || '0.0.0.0';\n\nserver.listen(PORT, HOST, () => {\n  console.log(`🚀 Media Server running on ${HOST}:${PORT}`);\n  console.log(`🌐 External access: https://${process.env.ANNOUNCED_IP || HOST}:${PORT}/`);\n  console.log('📡 Socket.IO server ready for WebSocket connections');\n});\nEOF\n\n# Set environment variables and start the server\nexport NODE_ENV=production\nexport LISTEN_IP=0.0.0.0\nexport ANNOUNCED_IP=************\nexport RECORDING_IP=0.0.0.0\nexport SSL_CERT_PATH=/opt/media-server/ssl/cert.pem\nexport SSL_KEY_PATH=/opt/media-server/ssl/key.pem\nexport ENABLE_HTTPS=true\nexport PORT=8080\n\n# Create systemd service for auto-start\ncat > /etc/systemd/system/media-server.service << EOF\n[Unit]\nDescription=WebRTC Media Server\nAfter=network.target\n\n[Service]\nType=simple\nUser=root\nWorkingDirectory=/opt/media-server\nEnvironment=NODE_ENV=production\nEnvironment=LISTEN_IP=0.0.0.0\nEnvironment=ANNOUNCED_IP=************\nEnvironment=RECORDING_IP=0.0.0.0\nEnvironment=SSL_CERT_PATH=/opt/media-server/ssl/cert.pem\nEnvironment=SSL_KEY_PATH=/opt/media-server/ssl/key.pem\nEnvironment=ENABLE_HTTPS=true\nEnvironment=PORT=8080\nExecStart=/usr/bin/node server.js\nRestart=always\nRestartSec=10\n\n[Install]\nWantedBy=multi-user.target\nEOF\n\n# Enable and start the service\nsystemctl daemon-reload\nsystemctl enable media-server\nsystemctl start media-server\n\n# Wait a moment and check status\nsleep 5\nsystemctl status media-server\n\necho \"Native media server setup completed successfully\"\n"}, "metadata_fingerprint": "i6W_aDbeXCM=", "metadata_startup_script": null, "min_cpu_platform": "", "name": "webrtc-platform-dev-media-server", "network_interface": [{"access_config": [{"nat_ip": "************", "network_tier": "PREMIUM", "public_ptr_domain_name": ""}], "alias_ip_range": [], "internal_ipv6_prefix_length": 0, "ipv6_access_config": [], "ipv6_access_type": "", "ipv6_address": "", "name": "nic0", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "network_ip": "********", "nic_type": "", "queue_count": 0, "stack_type": "IPV4_ONLY", "subnetwork": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/regions/us-central1/subnetworks/webrtc-platform-dev-subnet", "subnetwork_project": "switcher-studio-233517"}], "network_performance_config": [], "params": [], "project": "switcher-studio-233517", "reservation_affinity": [], "resource_policies": [], "scheduling": [{"automatic_restart": true, "instance_termination_action": "", "local_ssd_recovery_timeout": [], "max_run_duration": [], "min_node_cpus": 0, "node_affinities": [], "on_host_maintenance": "MIGRATE", "on_instance_stop_action": [], "preemptible": false, "provisioning_model": "STANDARD"}], "scratch_disk": [], "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/instances/webrtc-platform-dev-media-server", "service_account": [{"email": "<EMAIL>", "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}], "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false, "enable_vtpm": true}], "tags": ["http-server", "https-server", "media-server", "ssh-server"], "tags_fingerprint": "5-v9p7SR5wE=", "terraform_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "timeouts": null, "zone": "us-central1-a"}, "sensitive_attributes": [[{"type": "get_attr", "value": "boot_disk"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "disk_encryption_key_raw"}]], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.media_server.google_compute_address.media_server_ip", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.registry.data.google_project.project", "module.webrtc_platform.module.registry.google_artifact_registry_repository.main", "module.webrtc_platform.module.registry.google_artifact_registry_repository_iam_member.cloudbuild_writer", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.media_server", "mode": "managed", "type": "google_container_cluster", "name": "media_cluster", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 2, "attributes": {"addons_config": [{"cloudrun_config": [], "config_connector_config": [], "dns_cache_config": [], "gce_persistent_disk_csi_driver_config": [{"enabled": true}], "gcp_filestore_csi_driver_config": [], "gcs_fuse_csi_driver_config": [], "gke_backup_agent_config": [], "horizontal_pod_autoscaling": [], "http_load_balancing": [], "network_policy_config": [{"disabled": false}], "ray_operator_config": [], "stateful_ha_config": []}], "allow_net_admin": null, "authenticator_groups_config": [], "binary_authorization": [{"enabled": false, "evaluation_mode": ""}], "cluster_autoscaling": [{"auto_provisioning_defaults": [], "auto_provisioning_locations": [], "autoscaling_profile": "BALANCED", "enabled": false, "resource_limits": []}], "cluster_ipv4_cidr": "********/16", "confidential_nodes": [], "cost_management_config": [], "database_encryption": [{"key_name": "", "state": "DECRYPTED"}], "datapath_provider": "", "default_max_pods_per_node": 110, "default_snat_status": [{"disabled": false}], "deletion_protection": true, "description": "", "dns_config": [], "enable_autopilot": false, "enable_cilium_clusterwide_network_policy": false, "enable_intranode_visibility": false, "enable_k8s_beta_apis": [], "enable_kubernetes_alpha": false, "enable_l4_ilb_subsetting": false, "enable_legacy_abac": false, "enable_multi_networking": false, "enable_shielded_nodes": true, "enable_tpu": false, "endpoint": "*************", "fleet": [], "gateway_api_config": [], "id": "projects/switcher-studio-233517/locations/us-central1/clusters/webrtc-platform-dev-media-cluster", "identity_service_config": [], "initial_node_count": 1, "ip_allocation_policy": [{"additional_pod_ranges_config": [], "cluster_ipv4_cidr_block": "********/16", "cluster_secondary_range_name": "gke-webrtc-platform-dev-media-cluster-pods-23ddb652", "pod_cidr_overprovision_config": [{"disabled": false}], "services_ipv4_cidr_block": "********/16", "services_secondary_range_name": "gke-webrtc-platform-dev-media-cluster-services-23ddb652", "stack_type": "IPV4"}], "label_fingerprint": "a9dc16a7", "location": "us-central1", "logging_config": [{"enable_components": ["SYSTEM_COMPONENTS", "WORKLOADS"]}], "logging_service": "logging.googleapis.com/kubernetes", "maintenance_policy": [{"daily_maintenance_window": [{"duration": "PT4H0M0S", "start_time": "03:00"}], "maintenance_exclusion": [], "recurring_window": []}], "master_auth": [{"client_certificate": "", "client_certificate_config": [{"issue_client_certificate": false}], "client_key": "", "cluster_ca_certificate": "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"}], "master_authorized_networks_config": [], "master_version": "1.33.2-gke.1240000", "mesh_certificates": [], "min_master_version": null, "monitoring_config": [{"advanced_datapath_observability_config": [{"enable_metrics": false, "enable_relay": false, "relay_mode": "DISABLED"}], "enable_components": ["SYSTEM_COMPONENTS", "DAEMONSET", "DEPLOYMENT", "STATEFULSET", "JOBSET", "STORAGE", "HPA", "POD", "CADVISOR", "KUBELET", "DCGM"], "managed_prometheus": [{"enabled": true}]}], "monitoring_service": "monitoring.googleapis.com/kubernetes", "name": "webrtc-platform-dev-media-cluster", "network": "projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "network_policy": [{"enabled": true, "provider": ""}], "networking_mode": "VPC_NATIVE", "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 100, "disk_type": "pd-balanced", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [{"cpu_cfs_quota": false, "cpu_cfs_quota_period": "", "cpu_manager_policy": "", "insecure_kubelet_readonly_port_enabled": "FALSE", "pod_pids_limit": 0}], "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-2", "metadata": {"disable-legacy-endpoints": "true"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": true, "reservation_affinity": [], "resource_labels": {"goog-gke-node-pool-provisioning-model": "spot"}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": ["media-server"], "taint": [], "workload_metadata_config": [{"mode": "GKE_METADATA"}]}], "node_locations": ["us-central1-a", "us-central1-b", "us-central1-c"], "node_pool": [{"autoscaling": [], "initial_node_count": 1, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-c/instanceGroupManagers/gke-webrtc-platform--webrtc-platform--8e66d272-grp", "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/instanceGroupManagers/gke-webrtc-platform--webrtc-platform--54c186b0-grp", "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-b/instanceGroupManagers/gke-webrtc-platform--webrtc-platform--92aed7dc-grp"], "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-c/instanceGroups/gke-webrtc-platform--webrtc-platform--8e66d272-grp", "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/instanceGroups/gke-webrtc-platform--webrtc-platform--54c186b0-grp", "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-b/instanceGroups/gke-webrtc-platform--webrtc-platform--92aed7dc-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "webrtc-platform-dev-media-nodes", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": true, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "********/16", "pod_range": "gke-webrtc-platform-dev-media-cluster-pods-23ddb652"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 100, "disk_type": "pd-balanced", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [{"cpu_cfs_quota": false, "cpu_cfs_quota_period": "", "cpu_manager_policy": "", "insecure_kubelet_readonly_port_enabled": "FALSE", "pod_pids_limit": 0}], "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-2", "metadata": {"disable-legacy-endpoints": "true"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": true, "reservation_affinity": [], "resource_labels": {"goog-gke-node-pool-provisioning-model": "spot"}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": ["media-server"], "taint": [], "workload_metadata_config": [{"mode": "GKE_METADATA"}]}], "node_count": 1, "node_locations": ["us-central1-a", "us-central1-b", "us-central1-c"], "placement_policy": [], "queued_provisioning": [], "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.33.2-gke.1240000"}], "node_pool_auto_config": [{"network_tags": [], "node_kubelet_config": [{"insecure_kubelet_readonly_port_enabled": "FALSE"}], "resource_manager_tags": {}}], "node_pool_defaults": [{"node_config_defaults": [{"containerd_config": [], "insecure_kubelet_readonly_port_enabled": "FALSE", "logging_variant": "DEFAULT"}]}], "node_version": "1.33.2-gke.1240000", "notification_config": [{"pubsub": [{"enabled": false, "filter": [], "topic": ""}]}], "operation": null, "private_cluster_config": [{"enable_private_endpoint": false, "enable_private_nodes": true, "master_global_access_config": [{"enabled": false}], "master_ipv4_cidr_block": "**********/28", "peering_name": "", "private_endpoint": "**********", "private_endpoint_subnetwork": "", "public_endpoint": "*************"}], "private_ipv6_google_access": "", "project": "switcher-studio-233517", "release_channel": [{"channel": "REGULAR"}], "remove_default_node_pool": true, "resource_labels": {}, "resource_usage_export_config": [], "security_posture_config": [{"mode": "BASIC", "vulnerability_mode": "VULNERABILITY_MODE_UNSPECIFIED"}], "self_link": "https://container.googleapis.com/v1/projects/switcher-studio-233517/locations/us-central1/clusters/webrtc-platform-dev-media-cluster", "service_external_ips_config": [{"enabled": false}], "services_ipv4_cidr": "********/16", "subnetwork": "projects/switcher-studio-233517/regions/us-central1/subnetworks/webrtc-platform-dev-subnet", "timeouts": null, "tpu_ipv4_cidr_block": "", "vertical_pod_autoscaling": [], "workload_identity_config": [{"workload_pool": "switcher-studio-233517.svc.id.goog"}]}, "sensitive_attributes": [[{"type": "get_attr", "value": "master_auth"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "client_key"}]], "identity_schema_version": 0, "private": "************************************************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.registry.data.google_project.project", "module.webrtc_platform.module.registry.google_artifact_registry_repository.main", "module.webrtc_platform.module.registry.google_artifact_registry_repository_iam_member.cloudbuild_writer", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"]}]}, {"module": "module.webrtc_platform.module.media_server", "mode": "managed", "type": "google_container_node_pool", "name": "media_nodes", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"autoscaling": [], "cluster": "webrtc-platform-dev-media-cluster", "id": "projects/switcher-studio-233517/locations/us-central1/clusters/webrtc-platform-dev-media-cluster/nodePools/webrtc-platform-dev-media-nodes", "initial_node_count": 1, "instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-c/instanceGroupManagers/gke-webrtc-platform--webrtc-platform--8e66d272-grp", "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/instanceGroupManagers/gke-webrtc-platform--webrtc-platform--54c186b0-grp", "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-b/instanceGroupManagers/gke-webrtc-platform--webrtc-platform--92aed7dc-grp"], "location": "us-central1", "managed_instance_group_urls": ["https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-c/instanceGroups/gke-webrtc-platform--webrtc-platform--8e66d272-grp", "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/instanceGroups/gke-webrtc-platform--webrtc-platform--54c186b0-grp", "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-b/instanceGroups/gke-webrtc-platform--webrtc-platform--92aed7dc-grp"], "management": [{"auto_repair": true, "auto_upgrade": true}], "max_pods_per_node": 110, "name": "webrtc-platform-dev-media-nodes", "name_prefix": "", "network_config": [{"additional_node_network_configs": [], "additional_pod_network_configs": [], "create_pod_range": false, "enable_private_nodes": true, "network_performance_config": [], "pod_cidr_overprovision_config": [], "pod_ipv4_cidr_block": "********/16", "pod_range": "gke-webrtc-platform-dev-media-cluster-pods-23ddb652"}], "node_config": [{"advanced_machine_features": [], "boot_disk_kms_key": "", "confidential_nodes": [], "containerd_config": [], "disk_size_gb": 100, "disk_type": "pd-balanced", "effective_taints": [], "enable_confidential_storage": false, "ephemeral_storage_local_ssd_config": [], "fast_socket": [], "gcfs_config": [], "guest_accelerator": [], "gvnic": [], "host_maintenance_policy": [], "image_type": "COS_CONTAINERD", "kubelet_config": [{"cpu_cfs_quota": false, "cpu_cfs_quota_period": "", "cpu_manager_policy": "", "insecure_kubelet_readonly_port_enabled": "FALSE", "pod_pids_limit": 0}], "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "linux_node_config": [], "local_nvme_ssd_block_config": [], "local_ssd_count": 0, "logging_variant": "DEFAULT", "machine_type": "e2-standard-2", "metadata": {"disable-legacy-endpoints": "true"}, "min_cpu_platform": "", "node_group": "", "oauth_scopes": ["https://www.googleapis.com/auth/cloud-platform"], "preemptible": true, "reservation_affinity": [], "resource_labels": {"goog-gke-node-pool-provisioning-model": "spot"}, "resource_manager_tags": {}, "secondary_boot_disks": [], "service_account": "<EMAIL>", "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false}], "sole_tenant_config": [], "spot": false, "tags": ["media-server"], "taint": [], "workload_metadata_config": [{"mode": "GKE_METADATA"}]}], "node_count": 1, "node_locations": ["us-central1-a", "us-central1-b", "us-central1-c"], "operation": null, "placement_policy": [], "project": "switcher-studio-233517", "queued_provisioning": [], "timeouts": null, "upgrade_settings": [{"blue_green_settings": [], "max_surge": 1, "max_unavailable": 0, "strategy": "SURGE"}], "version": "1.33.2-gke.1240000"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.media_server.google_container_cluster.media_cluster", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.registry.data.google_project.project", "module.webrtc_platform.module.registry.google_artifact_registry_repository.main", "module.webrtc_platform.module.registry.google_artifact_registry_repository_iam_member.cloudbuild_writer", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"]}]}, {"module": "module.webrtc_platform.module.media_server", "mode": "managed", "type": "kubernetes_deployment", "name": "media_server", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "default/media-server", "metadata": [{"annotations": {}, "generate_name": "", "generation": 1, "labels": {"app": "media-server"}, "name": "media-server", "namespace": "default", "resource_version": "1755788499984383023", "uid": "5dad7542-c539-4bb1-9711-ba41844c021c"}], "spec": [{"min_ready_seconds": 0, "paused": false, "progress_deadline_seconds": 600, "replicas": "1", "revision_history_limit": 10, "selector": [{"match_expressions": [], "match_labels": {"app": "media-server"}}], "strategy": [{"rolling_update": [{"max_surge": "25%", "max_unavailable": "25%"}], "type": "RollingUpdate"}], "template": [{"metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {"app": "media-server"}, "name": "", "namespace": "", "resource_version": "", "uid": ""}], "spec": [{"active_deadline_seconds": 0, "affinity": [], "automount_service_account_token": true, "container": [{"args": [], "command": [], "env": [{"name": "SSL_CERT_PATH", "value": "/app/ssl/cert.pem", "value_from": []}, {"name": "SSL_KEY_PATH", "value": "/app/ssl/key.pem", "value_from": []}], "env_from": [{"config_map_ref": [{"name": "media-server-config", "optional": false}], "prefix": "", "secret_ref": []}], "image": "us-central1-docker.pkg.dev/switcher-studio-233517/webrtc-platform-dev-repo/media-server:latest", "image_pull_policy": "Always", "lifecycle": [], "liveness_probe": [{"exec": [], "failure_threshold": 3, "grpc": [], "http_get": [{"host": "", "http_header": [], "path": "/", "port": "8080", "scheme": "HTTPS"}], "initial_delay_seconds": 30, "period_seconds": 10, "success_threshold": 1, "tcp_socket": [], "timeout_seconds": 1}], "name": "media-server", "port": [{"container_port": 8080, "host_ip": "", "host_port": 0, "name": "", "protocol": "TCP"}, {"container_port": 40000, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40001, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40002, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40003, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40004, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40005, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40006, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40007, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40008, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40009, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40010, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40011, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40012, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40013, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40014, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40015, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40016, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40017, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40018, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40019, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40020, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40021, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40022, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40023, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40024, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40025, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40026, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40027, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40028, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40029, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40030, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40031, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40032, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40033, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40034, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40035, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40036, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40037, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40038, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40039, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40040, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40041, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40042, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40043, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40044, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40045, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40046, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40047, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40048, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40049, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40050, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40051, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40052, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40053, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40054, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40055, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40056, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40057, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40058, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40059, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40060, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40061, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40062, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40063, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40064, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40065, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40066, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40067, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40068, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40069, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40070, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40071, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40072, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40073, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40074, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40075, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40076, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40077, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40078, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40079, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40080, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40081, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40082, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40083, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40084, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40085, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40086, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40087, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40088, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40089, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40090, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40091, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40092, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40093, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40094, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40095, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40096, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40097, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40098, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40099, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}, {"container_port": 40100, "host_ip": "", "host_port": 0, "name": "", "protocol": "UDP"}], "readiness_probe": [{"exec": [], "failure_threshold": 3, "grpc": [], "http_get": [{"host": "", "http_header": [], "path": "/", "port": "8080", "scheme": "HTTPS"}], "initial_delay_seconds": 5, "period_seconds": 5, "success_threshold": 1, "tcp_socket": [], "timeout_seconds": 1}], "resources": [{"limits": {"cpu": "2", "memory": "4Gi"}, "requests": {"cpu": "1", "memory": "2Gi"}}], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "File", "tty": false, "volume_device": [], "volume_mount": [{"mount_path": "/app/ssl", "mount_propagation": "None", "name": "ssl-certs", "read_only": true, "sub_path": "", "sub_path_expr": ""}], "working_dir": ""}], "dns_config": [], "dns_policy": "ClusterFirst", "enable_service_links": true, "host_aliases": [], "host_ipc": false, "host_network": false, "host_pid": false, "hostname": "", "image_pull_secrets": [], "init_container": [{"args": [], "command": ["sh", "-c", "openssl req -x509 -newkey rsa:4096 -keyout /ssl/key.pem -out /ssl/cert.pem -days 365 -nodes -subj '/C=US/ST=CA/L=SF/O=WebRTC/CN=TBD' && chmod 644 /ssl/cert.pem && chmod 600 /ssl/key.pem"], "env": [], "env_from": [], "image": "alpine/openssl:latest", "image_pull_policy": "Always", "lifecycle": [], "liveness_probe": [], "name": "ssl-init", "port": [], "readiness_probe": [], "resources": [{"limits": {}, "requests": {}}], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "File", "tty": false, "volume_device": [], "volume_mount": [{"mount_path": "/ssl", "mount_propagation": "None", "name": "ssl-certs", "read_only": false, "sub_path": "", "sub_path_expr": ""}], "working_dir": ""}], "node_name": "", "node_selector": {}, "os": [], "priority_class_name": "", "readiness_gate": [], "restart_policy": "Always", "runtime_class_name": "", "scheduler_name": "default-scheduler", "security_context": [], "service_account_name": "media-server", "share_process_namespace": false, "subdomain": "", "termination_grace_period_seconds": 30, "toleration": [], "topology_spread_constraint": [], "volume": [{"aws_elastic_block_store": [], "azure_disk": [], "azure_file": [], "ceph_fs": [], "cinder": [], "config_map": [], "csi": [], "downward_api": [], "empty_dir": [{"medium": "", "size_limit": ""}], "ephemeral": [], "fc": [], "flex_volume": [], "flocker": [], "gce_persistent_disk": [], "git_repo": [], "glusterfs": [], "host_path": [], "iscsi": [], "local": [], "name": "ssl-certs", "nfs": [], "persistent_volume_claim": [], "photon_persistent_disk": [], "projected": [], "quobyte": [], "rbd": [], "secret": [], "vsphere_volume": []}]}]}]}], "timeouts": null, "wait_for_rollout": true}, "sensitive_attributes": [], "identity_schema_version": 1, "identity": {"api_version": "apps/v1", "kind": "Deployment", "name": "media-server", "namespace": "default"}, "private": "****************************************************************************************************************************************************************************", "dependencies": ["data.google_client_config.default", "module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.media_server.google_container_cluster.media_cluster", "module.webrtc_platform.module.media_server.google_container_node_pool.media_nodes", "module.webrtc_platform.module.media_server.kubernetes_config_map.media_server_config", "module.webrtc_platform.module.media_server.kubernetes_secret.media_server_ssl", "module.webrtc_platform.module.media_server.kubernetes_service_account.media_server", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.registry.data.google_project.project", "module.webrtc_platform.module.registry.google_artifact_registry_repository.main", "module.webrtc_platform.module.registry.google_artifact_registry_repository_iam_member.cloudbuild_writer", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"]}]}, {"module": "module.webrtc_platform.module.media_server", "mode": "managed", "type": "kubernetes_secret", "name": "media_server_ssl", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"binary_data": null, "binary_data_wo": null, "binary_data_wo_revision": null, "data": {}, "data_wo": null, "data_wo_revision": null, "id": "default/media-server-ssl", "immutable": false, "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "media-server-ssl", "namespace": "default", "resource_version": "1755785042458719024", "uid": "1fa8af7b-8ad1-48de-a5ab-a042cf5e2a75"}], "timeouts": null, "type": "Opaque", "wait_for_service_account_token": true}, "sensitive_attributes": [[{"type": "get_attr", "value": "binary_data"}], [{"type": "get_attr", "value": "data"}]], "identity_schema_version": 1, "identity": {"api_version": "v1", "kind": "Secret", "name": "media-server-ssl", "namespace": "default"}, "private": "************************************************************************************", "dependencies": ["data.google_client_config.default", "module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.media_server.google_container_cluster.media_cluster", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.registry.data.google_project.project", "module.webrtc_platform.module.registry.google_artifact_registry_repository.main", "module.webrtc_platform.module.registry.google_artifact_registry_repository_iam_member.cloudbuild_writer", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"]}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_firewall", "name": "allow_health_check", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": [], "protocol": "tcp"}], "creation_timestamp": "2025-08-19T07:41:53.513-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-health-check", "log_config": [], "name": "webrtc-platform-dev-vpc-allow-health-check", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-health-check", "source_ranges": ["***********/22", "**********/16"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": ["http-server"], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"]}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_firewall", "name": "allow_http_https", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": ["80", "443"], "protocol": "tcp"}], "creation_timestamp": "2025-08-19T07:41:53.616-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-http-https", "log_config": [], "name": "webrtc-platform-dev-vpc-allow-http-https", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-http-https", "source_ranges": ["0.0.0.0/0"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": ["http-server", "https-server"], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"]}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_firewall", "name": "allow_internal", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": [], "protocol": "icmp"}, {"ports": [], "protocol": "tcp"}, {"ports": [], "protocol": "udp"}], "creation_timestamp": "2025-08-19T07:41:52.829-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-internal", "log_config": [], "name": "webrtc-platform-dev-vpc-allow-internal", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-internal", "source_ranges": ["10.0.0.0/24"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": [], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"]}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_firewall", "name": "allow_ssh", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": ["22"], "protocol": "tcp"}], "creation_timestamp": "2025-08-19T07:41:53.494-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-ssh", "log_config": [], "name": "webrtc-platform-dev-vpc-allow-ssh", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-ssh", "source_ranges": ["0.0.0.0/0"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": ["ssh-server"], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"]}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_firewall", "name": "allow_turn_relay", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": ["49152-49252"], "protocol": "udp"}], "creation_timestamp": "2025-08-19T07:41:53.312-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-turn-relay", "log_config": [], "name": "webrtc-platform-dev-vpc-allow-turn-relay", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-turn-relay", "source_ranges": ["0.0.0.0/0"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": ["turn-server"], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"]}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_firewall", "name": "allow_turn_stun", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": ["3478", "5349"], "protocol": "tcp"}, {"ports": ["3478", "5349"], "protocol": "udp"}], "creation_timestamp": "2025-08-19T07:41:52.982-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-turn-stun", "log_config": [], "name": "webrtc-platform-dev-vpc-allow-turn-stun", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-turn-stun", "source_ranges": ["0.0.0.0/0"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": ["turn-server"], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"]}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_network", "name": "vpc", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "internal_ipv6_range": "", "mtu": 0, "name": "webrtc-platform-dev-vpc", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "7708684044095534071", "project": "switcher-studio-233517", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"]}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_router", "name": "router", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"bgp": [], "creation_timestamp": "2025-08-19T07:41:53.283-07:00", "description": "", "encrypted_interconnect_router": false, "id": "projects/switcher-studio-233517/regions/us-central1/routers/webrtc-platform-dev-vpc-router", "name": "webrtc-platform-dev-vpc-router", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "project": "switcher-studio-233517", "region": "us-central1", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/regions/us-central1/routers/webrtc-platform-dev-vpc-router", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"]}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_router_nat", "name": "nat", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"auto_network_tier": "PREMIUM", "drain_nat_ips": [], "enable_dynamic_port_allocation": false, "enable_endpoint_independent_mapping": false, "endpoint_types": ["ENDPOINT_TYPE_VM"], "icmp_idle_timeout_sec": 30, "id": "switcher-studio-233517/us-central1/webrtc-platform-dev-vpc-router/webrtc-platform-dev-vpc-nat", "log_config": [{"enable": true, "filter": "ERRORS_ONLY"}], "max_ports_per_vm": 0, "min_ports_per_vm": 0, "name": "webrtc-platform-dev-vpc-nat", "nat_ip_allocate_option": "AUTO_ONLY", "nat_ips": [], "project": "switcher-studio-233517", "region": "us-central1", "router": "webrtc-platform-dev-vpc-router", "rules": [], "source_subnetwork_ip_ranges_to_nat": "ALL_SUBNETWORKS_ALL_IP_RANGES", "subnetwork": [], "tcp_established_idle_timeout_sec": 1200, "tcp_time_wait_timeout_sec": 120, "tcp_transitory_idle_timeout_sec": 30, "timeouts": null, "udp_idle_timeout_sec": 30}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router"]}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_subnetwork", "name": "subnet", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"creation_timestamp": "2025-08-19T07:41:53.415-07:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "********", "id": "projects/switcher-studio-233517/regions/us-central1/subnetworks/webrtc-platform-dev-subnet", "internal_ipv6_prefix": "", "ip_cidr_range": "10.0.0.0/24", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [], "name": "webrtc-platform-dev-subnet", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "private_ip_google_access": true, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "switcher-studio-233517", "purpose": "PRIVATE", "region": "us-central1", "role": "", "secondary_ip_range": [{"ip_cidr_range": "********/16", "range_name": "gke-webrtc-platform-dev-media-cluster-pods-23ddb652"}, {"ip_cidr_range": "********/16", "range_name": "gke-webrtc-platform-dev-media-cluster-services-23ddb652"}], "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/regions/us-central1/subnetworks/webrtc-platform-dev-subnet", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"]}]}, {"module": "module.webrtc_platform.module.registry", "mode": "data", "type": "google_project", "name": "project", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"auto_create_network": null, "billing_account": "016427-C76683-997324", "deletion_policy": "DELETE", "effective_labels": {}, "folder_id": "", "id": "projects/switcher-studio-233517", "labels": {}, "name": "Switcher Studio", "number": "************", "org_id": "************", "project_id": "switcher-studio-233517", "skip_delete": null, "terraform_labels": {}}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.webrtc_platform.module.registry", "mode": "managed", "type": "google_artifact_registry_repository", "name": "main", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"cleanup_policies": [], "cleanup_policy_dry_run": false, "create_time": "2025-08-19T14:41:29.127132Z", "description": "Docker repository for WebRTC platform containers", "docker_config": [], "effective_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "format": "DOCKER", "id": "projects/switcher-studio-233517/locations/us-central1/repositories/webrtc-platform-dev-repo", "kms_key_name": "", "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "location": "us-central1", "maven_config": [], "mode": "STANDARD_REPOSITORY", "name": "webrtc-platform-dev-repo", "project": "switcher-studio-233517", "remote_repository_config": [], "repository_id": "webrtc-platform-dev-repo", "terraform_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "timeouts": null, "update_time": "2025-08-21T13:31:44.041839Z", "virtual_repository_config": []}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"]}]}, {"module": "module.webrtc_platform.module.registry", "mode": "managed", "type": "google_artifact_registry_repository_iam_member", "name": "cloudbuild_writer", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY8vRfHWME=", "id": "projects/switcher-studio-233517/locations/us-central1/repositories/webrtc-platform-dev-repo/roles/artifactregistry.writer/serviceAccount:<EMAIL>", "location": "us-central1", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "repository": "projects/switcher-studio-233517/locations/us-central1/repositories/webrtc-platform-dev-repo", "role": "roles/artifactregistry.writer"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.registry.data.google_project.project", "module.webrtc_platform.module.registry.google_artifact_registry_repository.main"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "media_artifact_registry_reader", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY84GWEkwg=", "id": "switcher-studio-233517/roles/artifactregistry.reader/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/artifactregistry.reader"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.media_server"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "media_logging", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY84GWEkwg=", "id": "switcher-studio-233517/roles/logging.logWriter/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/logging.logWriter"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.media_server"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "media_monitoring", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY84GWEkwg=", "id": "switcher-studio-233517/roles/monitoring.metricWriter/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/monitoring.metricWriter"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.media_server"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "media_secretmanager", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY84GWEkwg=", "id": "switcher-studio-233517/roles/secretmanager.secretAccessor/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/secretmanager.secretAccessor"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.media_server"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "media_storage", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY84GWEkwg=", "id": "switcher-studio-233517/roles/storage.objectAdmin/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/storage.objectAdmin"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.media_server"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "signaling_logging", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY84GWEkwg=", "id": "switcher-studio-233517/roles/logging.logWriter/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/logging.logWriter"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.signaling_server"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "signaling_monitoring", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY84GWEkwg=", "id": "switcher-studio-233517/roles/monitoring.metricWriter/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/monitoring.metricWriter"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.signaling_server"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "signaling_secretmanager", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY84GWEkwg=", "id": "switcher-studio-233517/roles/secretmanager.secretAccessor/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/secretmanager.secretAccessor"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.signaling_server"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "turn_logging", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY84GWEkwg=", "id": "switcher-studio-233517/roles/logging.logWriter/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/logging.logWriter"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.turn_server"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "turn_monitoring", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY84GWEkwg=", "id": "switcher-studio-233517/roles/monitoring.metricWriter/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/monitoring.metricWriter"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.turn_server"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "turn_secretmanager", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY84GWEkwg=", "id": "switcher-studio-233517/roles/secretmanager.secretAccessor/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/secretmanager.secretAccessor"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.turn_server"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_secret_manager_secret", "name": "turn_password", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": {}, "create_time": "2025-08-19T14:41:27.815334Z", "effective_annotations": {}, "effective_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "expire_time": "", "id": "projects/switcher-studio-233517/secrets/webrtc-platform-dev-turn-password", "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "name": "projects/************/secrets/webrtc-platform-dev-turn-password", "project": "switcher-studio-233517", "replication": [{"auto": [{"customer_managed_encryption": []}], "user_managed": []}], "rotation": [], "secret_id": "webrtc-platform-dev-turn-password", "terraform_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "timeouts": null, "topics": [], "ttl": null, "version_aliases": {}, "version_destroy_ttl": ""}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_secret_manager_secret", "name": "turn_username", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": {}, "create_time": "2025-08-19T14:41:27.812052Z", "effective_annotations": {}, "effective_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "expire_time": "", "id": "projects/switcher-studio-233517/secrets/webrtc-platform-dev-turn-username", "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "name": "projects/************/secrets/webrtc-platform-dev-turn-username", "project": "switcher-studio-233517", "replication": [{"auto": [{"customer_managed_encryption": []}], "user_managed": []}], "rotation": [], "secret_id": "webrtc-platform-dev-turn-username", "terraform_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "timeouts": null, "topics": [], "ttl": null, "version_aliases": {}, "version_destroy_ttl": ""}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_secret_manager_secret_version", "name": "turn_password", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"create_time": "2025-08-19T14:41:34.663946Z", "deletion_policy": "DELETE", "destroy_time": "", "enabled": true, "id": "projects/************/secrets/webrtc-platform-dev-turn-password/versions/1", "is_secret_data_base64": false, "name": "projects/************/secrets/webrtc-platform-dev-turn-password/versions/1", "secret": "projects/switcher-studio-233517/secrets/webrtc-platform-dev-turn-password", "secret_data": "webrtc123", "timeouts": null, "version": "1"}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_data"}]], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_secret_manager_secret_version", "name": "turn_username", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"create_time": "2025-08-19T14:41:33.363446Z", "deletion_policy": "DELETE", "destroy_time": "", "enabled": true, "id": "projects/************/secrets/webrtc-platform-dev-turn-username/versions/1", "is_secret_data_base64": false, "name": "projects/************/secrets/webrtc-platform-dev-turn-username/versions/1", "secret": "projects/switcher-studio-233517/secrets/webrtc-platform-dev-turn-username", "secret_data": "webrtc", "timeouts": null, "version": "1"}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_data"}]], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_service_account", "name": "media_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "webrtc-platform-dev-media", "create_ignore_already_exists": null, "description": "Service account for the WebRTC media server", "disabled": false, "display_name": "WebRTC Media Server Service Account", "email": "<EMAIL>", "id": "projects/switcher-studio-233517/serviceAccounts/<EMAIL>", "member": "serviceAccount:<EMAIL>", "name": "projects/switcher-studio-233517/serviceAccounts/<EMAIL>", "project": "switcher-studio-233517", "timeouts": null, "unique_id": "100023241196401491422"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_service_account", "name": "signaling_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "webrtc-platform-dev-signaling", "create_ignore_already_exists": null, "description": "Service account for the WebRTC signaling server", "disabled": false, "display_name": "WebRTC Signaling Server Service Account", "email": "<EMAIL>", "id": "projects/switcher-studio-233517/serviceAccounts/<EMAIL>", "member": "serviceAccount:<EMAIL>", "name": "projects/switcher-studio-233517/serviceAccounts/<EMAIL>", "project": "switcher-studio-233517", "timeouts": null, "unique_id": "114737965357682287212"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_service_account", "name": "turn_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "webrtc-platform-dev-turn", "create_ignore_already_exists": null, "description": "Service account for the TURN server", "disabled": false, "display_name": "TURN Server Service Account", "email": "<EMAIL>", "id": "projects/switcher-studio-233517/serviceAccounts/<EMAIL>", "member": "serviceAccount:<EMAIL>", "name": "projects/switcher-studio-233517/serviceAccounts/<EMAIL>", "project": "switcher-studio-233517", "timeouts": null, "unique_id": "100911814038303169451"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"]}]}], "check_results": null}