# Development Environment Variables
# Copy this file to terraform.tfvars and update with your values

# Required Variables
project_id = "your-gcp-project-id"
region     = "us-central1"
zone       = "us-central1-a"

# Application Configuration
app_name    = "webrtc-platform"
domain_name = ""  # Not needed for dev

# Docker Images (update after building)
signaling_server_image = "gcr.io/your-project-id/webrtc-signaling:latest"
media_server_image     = "gcr.io/your-project-id/media-server:latest"
turn_server_image      = "coturn/coturn:latest"

# TURN Server Configuration (development credentials)
turn_username = "webrtc"
turn_password = "webrtc123"

# Monitoring Configuration (optional for dev)
notification_email = ""  # Leave empty to disable notifications
