{"Modules": [{"Key": "", "Source": "", "Dir": "."}, {"Key": "webrtc_platform", "Source": "../..", "Dir": "../.."}, {"Key": "webrtc_platform.cloud_run", "Source": "./modules/cloud-run", "Dir": "../../modules/cloud-run"}, {"Key": "webrtc_platform.compute", "Source": "./modules/compute", "Dir": "../../modules/compute"}, {"Key": "webrtc_platform.media_server", "Source": "./modules/media-server-vm", "Dir": "../../modules/media-server-vm"}, {"Key": "webrtc_platform.monitoring", "Source": "./modules/monitoring", "Dir": "../../modules/monitoring"}, {"Key": "webrtc_platform.networking", "Source": "./modules/networking", "Dir": "../../modules/networking"}, {"Key": "webrtc_platform.registry", "Source": "./modules/registry", "Dir": "../../modules/registry"}, {"Key": "webrtc_platform.security", "Source": "./modules/security", "Dir": "../../modules/security"}, {"Key": "webrtc_platform.signaling_server", "Source": "./modules/cloud-run", "Dir": "../../modules/cloud-run"}]}