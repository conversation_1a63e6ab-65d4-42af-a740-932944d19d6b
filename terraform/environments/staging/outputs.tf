# Staging Environment Outputs

output "signaling_server_url" {
  description = "URL of the signaling server"
  value       = module.webrtc_platform.signaling_server_url
}

output "media_server_url" {
  description = "URL of the media server"
  value       = module.webrtc_platform.media_server_url
}

output "turn_server_ip" {
  description = "External IP address of the TURN server"
  value       = module.webrtc_platform.turn_server_ip
}

output "monitoring_dashboard_url" {
  description = "URL of the monitoring dashboard"
  value       = module.webrtc_platform.monitoring_dashboard_url
}

output "environment_config" {
  description = "Environment configuration for application deployment"
  value       = module.webrtc_platform.environment_config
  sensitive   = true
}

output "deployment_commands" {
  description = "Commands to deploy applications to the infrastructure"
  sensitive   = true
  value       = module.webrtc_platform.deployment_commands
}
