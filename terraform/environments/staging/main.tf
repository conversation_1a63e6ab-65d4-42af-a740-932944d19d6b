# Staging Environment Configuration

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 5.0"
    }
  }

  # Configure remote state backend for staging
  # backend "gcs" {
  #   bucket = "your-terraform-state-bucket"
  #   prefix = "webrtc-platform/staging"
  # }
}

provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Local values for staging environment
locals {
  environment = "staging"
  name_prefix = "${var.app_name}-${local.environment}"
  common_labels = {
    project     = var.app_name
    environment = local.environment
    managed-by  = "terraform"
    region      = var.region
  }
}

# Call the main module with staging-specific settings
module "webrtc_platform" {
  source = "../.."

  # Project Configuration
  project_id  = var.project_id
  region      = var.region
  zone        = var.zone
  environment = local.environment

  # Application Configuration
  app_name    = var.app_name
  domain_name = var.domain_name

  # Networking Configuration
  vpc_name    = "${local.name_prefix}-vpc"
  subnet_cidr = "10.1.0.0/24"

  # Docker Images
  signaling_server_image = var.signaling_server_image
  media_server_image     = var.media_server_image

  # Cloud Run Configuration (production-like but smaller)
  cloud_run_cpu          = "1"
  cloud_run_memory       = "2Gi"
  cloud_run_max_instances = 5

  # TURN Server Configuration
  turn_server_machine_type = "e2-small"
  turn_server_image       = var.turn_server_image
  turn_username           = var.turn_username
  turn_password           = var.turn_password

  # Monitoring Configuration
  enable_monitoring  = true
  notification_email = var.notification_email

  # Security Configuration (more restrictive than dev)
  allowed_source_ranges = var.allowed_source_ranges

  # Feature Flags
  enable_ssl    = var.enable_ssl
  enable_cdn    = false
  enable_backup = true

  # Resource Labels
  labels = local.common_labels
}
