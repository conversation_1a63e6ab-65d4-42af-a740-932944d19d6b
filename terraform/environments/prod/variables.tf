# Production Environment Variables

variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
}

variable "region" {
  description = "Primary deployment region"
  type        = string
  default     = "us-central1"
}

variable "zone" {
  description = "Primary deployment zone"
  type        = string
  default     = "us-central1-a"
}

variable "app_name" {
  description = "Application name prefix"
  type        = string
  default     = "webrtc-platform"
}

variable "domain_name" {
  description = "Custom domain name for the application"
  type        = string
}

variable "signaling_server_image" {
  description = "Docker image for the signaling server"
  type        = string
}

variable "media_server_image" {
  description = "Docker image for the media server"
  type        = string
}

variable "turn_server_image" {
  description = "Docker image for the TURN server"
  type        = string
  default     = "coturn/coturn:latest"
}

variable "turn_username" {
  description = "TURN server username"
  type        = string
  sensitive   = true
}

variable "turn_password" {
  description = "TURN server password"
  type        = string
  sensitive   = true
}

variable "notification_email" {
  description = "Email address for monitoring notifications"
  type        = string
}

variable "allowed_source_ranges" {
  description = "CIDR blocks allowed to access the services"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "enable_ssl" {
  description = "Enable SSL certificates for custom domains"
  type        = bool
  default     = true
}

variable "enable_cdn" {
  description = "Enable Cloud CDN for static content"
  type        = bool
  default     = true
}
