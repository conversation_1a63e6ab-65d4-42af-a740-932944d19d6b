# Production Environment Configuration

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 5.0"
    }
  }

  # Configure remote state backend for production
  # backend "gcs" {
  #   bucket = "your-terraform-state-bucket"
  #   prefix = "webrtc-platform/prod"
  # }
}

provider "google" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

provider "google-beta" {
  project = var.project_id
  region  = var.region
  zone    = var.zone
}

# Local values for production environment
locals {
  environment = "prod"
  name_prefix = "${var.app_name}-${local.environment}"
  common_labels = {
    project     = var.app_name
    environment = local.environment
    managed-by  = "terraform"
    region      = var.region
  }
}

# Call the main module with production-specific settings
module "webrtc_platform" {
  source = "../.."

  # Project Configuration
  project_id  = var.project_id
  region      = var.region
  zone        = var.zone
  environment = local.environment

  # Application Configuration
  app_name    = var.app_name
  domain_name = var.domain_name

  # Networking Configuration
  vpc_name    = "${local.name_prefix}-vpc"
  subnet_cidr = "10.2.0.0/24"

  # Docker Images
  signaling_server_image = var.signaling_server_image
  media_server_image     = var.media_server_image

  # Cloud Run Configuration (full production resources)
  cloud_run_cpu          = "2"
  cloud_run_memory       = "4Gi"
  cloud_run_max_instances = 20

  # TURN Server Configuration (larger instance for production)
  turn_server_machine_type = "e2-standard-2"
  turn_server_image       = var.turn_server_image
  turn_username           = var.turn_username
  turn_password           = var.turn_password

  # Monitoring Configuration
  enable_monitoring  = true
  notification_email = var.notification_email

  # Security Configuration (restrictive for production)
  allowed_source_ranges = var.allowed_source_ranges

  # Feature Flags (all enabled for production)
  enable_ssl    = var.enable_ssl
  enable_cdn    = var.enable_cdn
  enable_backup = true

  # Resource Labels
  labels = local.common_labels
}
