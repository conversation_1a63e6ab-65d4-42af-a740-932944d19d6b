# Artifact Registry Module for WebRTC Platform

# Artifact Registry Repository
resource "google_artifact_registry_repository" "main" {
  location      = var.region
  repository_id = var.repository_id
  description   = "Docker repository for WebRTC platform containers"
  format        = "DOCKER"
  project       = var.project_id

  labels = var.labels
}

# Artifact Registry Repository for npm packages
resource "google_artifact_registry_repository" "npm" {
  location      = var.region
  repository_id = "${var.repository_id}-npm"
  description   = "npm repository for WebRTC platform packages"
  format        = "NPM"
  project       = var.project_id

  labels = var.labels
}

# Note: Using npm repository directly, no need for separate storage bucket

# IAM binding to allow Cloud Build to push images
resource "google_artifact_registry_repository_iam_member" "cloudbuild_writer" {
  project    = var.project_id
  location   = google_artifact_registry_repository.main.location
  repository = google_artifact_registry_repository.main.name
  role       = "roles/artifactregistry.writer"
  member     = "serviceAccount:${data.google_project.project.number}@cloudbuild.gserviceaccount.com"
}

# Data source to get project information
data "google_project" "project" {
  project_id = var.project_id
}
