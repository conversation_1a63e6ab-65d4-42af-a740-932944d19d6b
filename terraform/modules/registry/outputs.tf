output "repository_url" {
  description = "URL of the Artifact Registry repository"
  value       = "${google_artifact_registry_repository.main.location}-docker.pkg.dev/${var.project_id}/${google_artifact_registry_repository.main.repository_id}"
}

output "npm_repository_url" {
  description = "URL of the npm Artifact Registry repository"
  value       = "${google_artifact_registry_repository.npm.location}-npm.pkg.dev/${var.project_id}/${google_artifact_registry_repository.npm.repository_id}"
}

# Note: Using npm repository directly

output "repository_name" {
  description = "Name of the Artifact Registry repository"
  value       = google_artifact_registry_repository.main.name
}

output "repository_id" {
  description = "ID of the Artifact Registry repository"
  value       = google_artifact_registry_repository.main.repository_id
}
