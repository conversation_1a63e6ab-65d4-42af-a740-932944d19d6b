output "vpc_name" {
  description = "Name of the VPC network"
  value       = google_compute_network.vpc.name
}

output "vpc_id" {
  description = "ID of the VPC network"
  value       = google_compute_network.vpc.id
}

output "subnet_name" {
  description = "Name of the subnet"
  value       = google_compute_subnetwork.subnet.name
}

output "subnet_id" {
  description = "ID of the subnet"
  value       = google_compute_subnetwork.subnet.id
}

output "subnet_cidr" {
  description = "CIDR block of the subnet"
  value       = google_compute_subnetwork.subnet.ip_cidr_range
}

output "firewall_rules" {
  description = "List of created firewall rules"
  value = [
    google_compute_firewall.allow_http_https.name,
    google_compute_firewall.allow_ssh.name,
    google_compute_firewall.allow_turn_stun.name,
    google_compute_firewall.allow_turn_relay.name,
    google_compute_firewall.allow_internal.name,
    google_compute_firewall.allow_health_check.name
  ]
}
