variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "notification_email" {
  description = "Email address for monitoring notifications"
  type        = string
  default     = ""
}

variable "signaling_service_name" {
  description = "Name of the signaling server service"
  type        = string
}

variable "media_service_name" {
  description = "Name of the media server service"
  type        = string
}

variable "turn_instance_name" {
  description = "Name of the TURN server instance"
  type        = string
}

variable "labels" {
  description = "Labels to apply to resources"
  type        = map(string)
  default     = {}
}
