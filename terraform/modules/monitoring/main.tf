# Monitoring Module for WebRTC Platform

# Notification Channel for alerts
resource "google_monitoring_notification_channel" "email" {
  count        = var.notification_email != "" ? 1 : 0
  display_name = "Email Notification Channel"
  type         = "email"
  project      = var.project_id

  labels = {
    email_address = var.notification_email
  }
}

# Alert Policy for Cloud Run CPU usage
resource "google_monitoring_alert_policy" "cloud_run_cpu" {
  display_name = "${var.name_prefix} Cloud Run High CPU Usage"
  project      = var.project_id
  combiner     = "OR"

  conditions {
    display_name = "Cloud Run CPU usage"
    
    condition_threshold {
      filter          = "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=~\"${var.signaling_service_name}|${var.media_service_name}\""
      duration        = "300s"
      comparison      = "COMPARISON_GT"
      threshold_value = 0.8

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_RATE"
      }
    }
  }

  notification_channels = var.notification_email != "" ? [google_monitoring_notification_channel.email[0].name] : []

  alert_strategy {
    auto_close = "1800s"
  }
}

# Alert Policy for Cloud Run Memory usage
resource "google_monitoring_alert_policy" "cloud_run_memory" {
  display_name = "${var.name_prefix} Cloud Run High Memory Usage"
  project      = var.project_id
  combiner     = "OR"

  conditions {
    display_name = "Cloud Run Memory usage"
    
    condition_threshold {
      filter          = "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=~\"${var.signaling_service_name}|${var.media_service_name}\""
      duration        = "300s"
      comparison      = "COMPARISON_GT"
      threshold_value = 0.9

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MEAN"
      }
    }
  }

  notification_channels = var.notification_email != "" ? [google_monitoring_notification_channel.email[0].name] : []

  alert_strategy {
    auto_close = "1800s"
  }
}

# Alert Policy for TURN server instance down
resource "google_monitoring_alert_policy" "turn_server_down" {
  display_name = "${var.name_prefix} TURN Server Instance Down"
  project      = var.project_id
  combiner     = "OR"

  conditions {
    display_name = "TURN Server Instance Up"
    
    condition_threshold {
      filter          = "resource.type=\"gce_instance\" AND resource.labels.instance_id=\"${var.turn_instance_name}\""
      duration        = "300s"
      comparison      = "COMPARISON_LT"
      threshold_value = 1

      aggregations {
        alignment_period   = "60s"
        per_series_aligner = "ALIGN_MEAN"
      }
    }
  }

  notification_channels = var.notification_email != "" ? [google_monitoring_notification_channel.email[0].name] : []

  alert_strategy {
    auto_close = "1800s"
  }
}

# Dashboard for WebRTC Platform monitoring
resource "google_monitoring_dashboard" "webrtc_dashboard" {
  dashboard_json = jsonencode({
    displayName = "${var.name_prefix} WebRTC Platform Dashboard"
    mosaicLayout = {
      tiles = [
        {
          width  = 6
          height = 4
          widget = {
            title = "Cloud Run Request Count"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=~\"${var.signaling_service_name}|${var.media_service_name}\""
                      aggregation = {
                        alignmentPeriod  = "60s"
                        perSeriesAligner = "ALIGN_RATE"
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
            }
          }
        },
        {
          width  = 6
          height = 4
          xPos   = 6
          widget = {
            title = "Cloud Run CPU Utilization"
            xyChart = {
              dataSets = [
                {
                  timeSeriesQuery = {
                    timeSeriesFilter = {
                      filter = "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=~\"${var.signaling_service_name}|${var.media_service_name}\""
                      aggregation = {
                        alignmentPeriod  = "60s"
                        perSeriesAligner = "ALIGN_MEAN"
                      }
                    }
                  }
                  plotType = "LINE"
                }
              ]
            }
          }
        }
      ]
    }
  })
  project = var.project_id
}
