# Media Server on Compute Engine Module

# Static IP for media server
resource "google_compute_address" "media_server_ip" {
  name         = "${var.name_prefix}-media-ip"
  region       = var.region
  project      = var.project_id
  address_type = "EXTERNAL"
}

# Media Server Instance
resource "google_compute_instance" "media_server" {
  name         = "${var.name_prefix}-media-server"
  machine_type = var.machine_type
  zone         = var.zone
  project      = var.project_id

  tags = ["media-server", "ssh-server", "http-server", "https-server"]

  boot_disk {
    initialize_params {
      image = "ubuntu-os-cloud/ubuntu-2204-lts"
      size  = 30
      type  = "pd-standard"
    }
  }

  network_interface {
    network    = var.vpc_name
    subnetwork = var.subnet_name
    
    access_config {
      nat_ip = google_compute_address.media_server_ip.address
    }
  }

  service_account {
    email  = var.service_account_email
    scopes = ["cloud-platform"]
  }

  metadata = {
    enable-oslogin = "TRUE"
    startup-script = templatefile("${path.module}/startup-script.sh", {
      listen_ip    = "0.0.0.0"
      announced_ip = google_compute_address.media_server_ip.address
      recording_ip = "0.0.0.0"
    })
  }

  labels = var.labels

  lifecycle {
    create_before_destroy = true
  }
}

# Health check for media server
resource "google_compute_health_check" "media_server" {
  name               = "${var.name_prefix}-media-health-check"
  project            = var.project_id
  check_interval_sec = 30
  timeout_sec        = 10

  http_health_check {
    port = "8080"
    request_path = "/"
  }
}

# Firewall rule for WebRTC media ports
resource "google_compute_firewall" "allow_webrtc_media" {
  name    = "${var.name_prefix}-allow-webrtc-media"
  network = var.vpc_name
  project = var.project_id

  allow {
    protocol = "udp"
    ports    = ["40000-40100"]  # WebRTC media port range
  }

  allow {
    protocol = "tcp"
    ports    = ["40000-40100"]  # WebRTC media port range (TCP fallback)
  }

  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["media-server"]
}

# Firewall rule for media server HTTP API
resource "google_compute_firewall" "allow_media_server_http" {
  name    = "${var.name_prefix}-allow-media-server-http"
  network = var.vpc_name
  project = var.project_id

  allow {
    protocol = "tcp"
    ports    = ["8080", "443"]
  }

  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["media-server"]
}

# Load Balancer for HTTPS termination
resource "google_compute_global_address" "media_lb_ip" {
  name    = "${var.name_prefix}-media-lb-ip"
  project = var.project_id
}

# Backend service for media server
resource "google_compute_backend_service" "media_backend" {
  name                  = "${var.name_prefix}-media-backend"
  project               = var.project_id
  protocol              = "HTTP"
  port_name             = "http"
  timeout_sec           = 30
  enable_cdn            = false
  load_balancing_scheme = "EXTERNAL"

  backend {
    group = google_compute_instance_group.media_group.self_link
  }

  health_checks = [google_compute_health_check.media_server.self_link]
}

# Instance group for media server
resource "google_compute_instance_group" "media_group" {
  name    = "${var.name_prefix}-media-group"
  zone    = var.zone
  project = var.project_id

  instances = [google_compute_instance.media_server.self_link]

  named_port {
    name = "http"
    port = "8080"
  }
}

# SSL certificate for media server
resource "google_compute_managed_ssl_certificate" "media_cert" {
  name    = "${var.name_prefix}-media-cert"
  project = var.project_id

  managed {
    domains = ["${var.name_prefix}-media.${var.domain_name}"]
  }
}

# HTTPS proxy
resource "google_compute_target_https_proxy" "media_proxy" {
  name             = "${var.name_prefix}-media-proxy"
  project          = var.project_id
  url_map          = google_compute_url_map.media_map.self_link
  ssl_certificates = [google_compute_managed_ssl_certificate.media_cert.self_link]
}

# URL map for media server
resource "google_compute_url_map" "media_map" {
  name            = "${var.name_prefix}-media-map"
  project         = var.project_id
  default_service = google_compute_backend_service.media_backend.self_link
}

# Global forwarding rule for HTTPS
resource "google_compute_global_forwarding_rule" "media_https" {
  name       = "${var.name_prefix}-media-https"
  project    = var.project_id
  target     = google_compute_target_https_proxy.media_proxy.self_link
  port_range = "443"
  ip_address = google_compute_global_address.media_lb_ip.address
}
