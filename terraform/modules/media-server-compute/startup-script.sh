#!/bin/bash

# Startup script for media server instance - Native installation
set -e

# Configure logging
exec > >(tee /var/log/startup-script.log)
exec 2>&1

echo "Starting native media server setup..."

# Update package lists and install required packages
apt-get update
apt-get install -y curl wget gnupg2 software-properties-common openssl

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Verify installation
echo "Node.js version: $(node --version)"
echo "NPM version: $(npm --version)"

# Create media server directory
mkdir -p /opt/media-server
cd /opt/media-server

# Create SSL certificates directory
mkdir -p /opt/media-server/ssl

# Generate self-signed SSL certificate for HTTPS
openssl req -x509 -newkey rsa:4096 -keyout /opt/media-server/ssl/key.pem -out /opt/media-server/ssl/cert.pem -days 365 -nodes -subj "/C=US/ST=CA/L=SF/O=WebRTC/CN=${announced_ip}"

# Set proper permissions for SSL certificates
chmod 644 /opt/media-server/ssl/cert.pem
chmod 600 /opt/media-server/ssl/key.pem

# Download and extract the actual media server code
echo "Downloading media server code from Cloud Storage..."
gsutil cp gs://webrtc-platform-dev-code/media-server.tar.gz .
tar -xzf media-server.tar.gz --strip-components=1
rm media-server.tar.gz

# Install dependencies
echo "Installing Node.js dependencies..."
npm install

# Set environment variables and start the server
export NODE_ENV=production
export LISTEN_IP=${listen_ip}
export ANNOUNCED_IP=${announced_ip}
export RECORDING_IP=${recording_ip}
export SSL_CERT_PATH=/opt/media-server/ssl/cert.pem
export SSL_KEY_PATH=/opt/media-server/ssl/key.pem
export ENABLE_HTTPS=true
export PORT=8080

# Create systemd service for auto-start
cat > /etc/systemd/system/media-server.service << EOF
[Unit]
Description=WebRTC Media Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/media-server
Environment=NODE_ENV=production
Environment=LISTEN_IP=${listen_ip}
Environment=ANNOUNCED_IP=${announced_ip}
Environment=RECORDING_IP=${recording_ip}
Environment=SSL_CERT_PATH=/opt/media-server/ssl/cert.pem
Environment=SSL_KEY_PATH=/opt/media-server/ssl/key.pem
Environment=ENABLE_HTTPS=true
Environment=PORT=8080
ExecStart=/usr/bin/node server.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
systemctl daemon-reload
systemctl enable media-server
systemctl start media-server

# Wait a moment and check status
sleep 5
systemctl status media-server

echo "Native media server setup completed successfully"
