output "cluster_name" {
  description = "Name of the GKE cluster"
  value       = google_container_cluster.media_cluster.name
}

output "cluster_endpoint" {
  description = "Endpoint of the GKE cluster"
  value       = google_container_cluster.media_cluster.endpoint
  sensitive   = true
}

output "cluster_ca_certificate" {
  description = "CA certificate of the GKE cluster"
  value       = google_container_cluster.media_cluster.master_auth[0].cluster_ca_certificate
  sensitive   = true
}

output "media_server_external_ip" {
  description = "External IP address of the media server LoadBalancer (to be created manually)"
  value       = "TBD"
}

output "media_server_url" {
  description = "URL of the media server (to be created manually)"
  value       = "TBD"
}

output "kubectl_config_command" {
  description = "Command to configure kubectl"
  value       = "gcloud container clusters get-credentials ${google_container_cluster.media_cluster.name} --region ${var.region} --project ${var.project_id}"
}
