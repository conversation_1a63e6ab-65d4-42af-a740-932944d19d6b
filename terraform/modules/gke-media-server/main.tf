# GKE Cluster for Media Server

# GKE Cluster
resource "google_container_cluster" "media_cluster" {
  name     = "${var.name_prefix}-media-cluster"
  location = var.region
  project  = var.project_id

  # We can't create a cluster with no node pool defined, but we want to only use
  # separately managed node pools. So we create the smallest possible default
  # node pool and immediately delete it.
  remove_default_node_pool = true
  initial_node_count       = 1

  network    = var.vpc_name
  subnetwork = var.subnet_name

  # Enable network policy for security
  network_policy {
    enabled = true
  }

  # Enable IP aliasing for better networking
  ip_allocation_policy {
    cluster_ipv4_cidr_block  = "********/16"
    services_ipv4_cidr_block = "********/16"
  }

  # Enable workload identity for secure service account access
  workload_identity_config {
    workload_pool = "${var.project_id}.svc.id.goog"
  }

  # Maintenance window
  maintenance_policy {
    daily_maintenance_window {
      start_time = "03:00"
    }
  }

  # Enable logging and monitoring
  logging_service    = "logging.googleapis.com/kubernetes"
  monitoring_service = "monitoring.googleapis.com/kubernetes"

  # Security settings
  master_auth {
    client_certificate_config {
      issue_client_certificate = false
    }
  }

  # Enable private cluster for security
  private_cluster_config {
    enable_private_nodes    = true
    enable_private_endpoint = false
    master_ipv4_cidr_block  = "**********/28"
  }

  # Enable network policy
  addons_config {
    network_policy_config {
      disabled = false
    }
  }
}

# Node Pool for Media Server
resource "google_container_node_pool" "media_nodes" {
  name       = "${var.name_prefix}-media-nodes"
  location   = var.region
  cluster    = google_container_cluster.media_cluster.name
  project    = var.project_id
  node_count = var.node_count

  node_config {
    preemptible  = var.use_preemptible_nodes
    machine_type = var.machine_type

    # Google recommends custom service accounts that have cloud-platform scope and permissions granted via IAM Roles.
    service_account = var.service_account_email
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]

    labels = var.labels

    tags = ["media-server"]

    # Enable workload identity
    workload_metadata_config {
      mode = "GKE_METADATA"
    }
  }

  # Auto-scaling configuration (only enable if more than 1 node)
  dynamic "autoscaling" {
    for_each = var.node_count > 1 ? [1] : []
    content {
      min_node_count = var.min_node_count
      max_node_count = var.max_node_count
    }
  }

  # Auto-upgrade and auto-repair
  management {
    auto_repair  = true
    auto_upgrade = true
  }
}

# Note: Kubernetes resources will be deployed manually after cluster creation
# This module only creates the GKE cluster and node pool
