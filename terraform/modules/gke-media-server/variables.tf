variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
}

variable "region" {
  description = "Google Cloud region"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "vpc_name" {
  description = "Name of the VPC network"
  type        = string
}

variable "subnet_name" {
  description = "Name of the subnet"
  type        = string
}

variable "media_server_image" {
  description = "Docker image for the media server"
  type        = string
}

variable "service_account_email" {
  description = "Service account email for the media server"
  type        = string
}

variable "announced_ip" {
  description = "External IP address to announce for WebRTC"
  type        = string
}

variable "labels" {
  description = "Labels to apply to resources"
  type        = map(string)
  default     = {}
}

# Node pool configuration
variable "node_count" {
  description = "Number of nodes in the node pool"
  type        = number
  default     = 1
}

variable "min_node_count" {
  description = "Minimum number of nodes in the node pool"
  type        = number
  default     = 1
}

variable "max_node_count" {
  description = "Maximum number of nodes in the node pool"
  type        = number
  default     = 5
}

variable "machine_type" {
  description = "Machine type for the nodes"
  type        = string
  default     = "e2-standard-2"
}

variable "use_preemptible_nodes" {
  description = "Use preemptible nodes for cost savings"
  type        = bool
  default     = true
}

# Pod configuration
variable "replicas" {
  description = "Number of media server replicas"
  type        = number
  default     = 1
}

variable "cpu_limit" {
  description = "CPU limit for media server pods"
  type        = string
  default     = "2"
}

variable "memory_limit" {
  description = "Memory limit for media server pods"
  type        = string
  default     = "4Gi"
}

variable "cpu_request" {
  description = "CPU request for media server pods"
  type        = string
  default     = "1"
}

variable "memory_request" {
  description = "Memory request for media server pods"
  type        = string
  default     = "2Gi"
}
