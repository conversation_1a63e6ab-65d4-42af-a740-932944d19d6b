# Compute Module for TURN Server

# Static IP for TURN server
resource "google_compute_address" "turn_server_ip" {
  name         = "${var.name_prefix}-turn-ip"
  region       = var.region
  project      = var.project_id
  address_type = "EXTERNAL"
}

# TURN Server Instance
resource "google_compute_instance" "turn_server" {
  name         = "${var.name_prefix}-turn-server"
  machine_type = var.machine_type
  zone         = var.zone
  project      = var.project_id

  tags = ["turn-server", "ssh-server"]

  boot_disk {
    initialize_params {
      image = "cos-cloud/cos-stable"
      size  = 20
      type  = "pd-standard"
    }
  }

  network_interface {
    network    = var.vpc_name
    subnetwork = var.subnet_name
    
    access_config {
      nat_ip = google_compute_address.turn_server_ip.address
    }
  }

  service_account {
    email  = var.service_account_email
    scopes = ["cloud-platform"]
  }

  metadata = {
    enable-oslogin = "TRUE"
    startup-script = templatefile("${path.module}/startup-script.sh", {
      turn_server_image    = var.turn_server_image
      turn_username        = var.turn_username
      turn_password_secret = var.turn_password_secret
      project_id          = var.project_id
    })
  }

  labels = var.labels

  lifecycle {
    create_before_destroy = true
  }
}

# Health check for TURN server
resource "google_compute_health_check" "turn_server" {
  name               = "${var.name_prefix}-turn-health-check"
  project            = var.project_id
  check_interval_sec = 30
  timeout_sec        = 10

  tcp_health_check {
    port = "3478"
  }
}
