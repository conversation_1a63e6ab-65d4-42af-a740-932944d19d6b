output "turn_server_ip" {
  description = "External IP address of the TURN server"
  value       = google_compute_address.turn_server_ip.address
}

output "turn_server_internal_ip" {
  description = "Internal IP address of the TURN server"
  value       = google_compute_instance.turn_server.network_interface[0].network_ip
}

output "turn_instance_name" {
  description = "Name of the TURN server instance"
  value       = google_compute_instance.turn_server.name
}

output "turn_instance_zone" {
  description = "Zone of the TURN server instance"
  value       = google_compute_instance.turn_server.zone
}
