#!/bin/bash

# Startup script for TURN server instance
set -e

# Configure logging
exec > >(tee /var/log/startup-script.log)
exec 2>&1

echo "Starting TURN server setup..."

# Install required packages
apt-get update
apt-get install -y docker.io

# Start Docker service
systemctl start docker
systemctl enable docker

# Configure Docker authentication for Artifact Registry
gcloud auth configure-docker --quiet

# Get TURN password from Secret Manager
TURN_PASSWORD=$(gcloud secrets versions access latest --secret="${turn_password_secret}" --project="${project_id}")

# Stop any existing coturn container
docker stop coturn-server 2>/dev/null || true
docker rm coturn-server 2>/dev/null || true

# Create coturn configuration directory
mkdir -p /etc/coturn

# Create coturn configuration file
cat > /etc/coturn/turnserver.conf << EOF
# coturn TURN server configuration for WebRTC

# Listening port for TURN/STUN
listening-port=3478

# TLS listening port (optional, for secure connections)
tls-listening-port=5349

# Relay ports range (for media)
min-port=49152
max-port=49252

# Enable verbose logging
verbose

# Log file (will go to stdout in Docker)
log-file=stdout

# Realm for authentication
realm=webrtc.local

# Server name
server-name=turn-server

# Authentication
# Use long-term credentials
lt-cred-mech

# Static user credentials
user=${turn_username}:$TURN_PASSWORD

# Allow loopback peers (for local testing)
allow-loopback-peers

# Disable RFC5780 support (can cause issues)
no-rfc5780

# Enable STUN
stun-only=false

# Disable software attributes
no-software-attribute
EOF

# Pull and run coturn container
echo "Pulling TURN server image..."
docker pull ${turn_server_image}

echo "Starting TURN server container..."
docker run -d \
    --name coturn-server \
    --restart unless-stopped \
    --network host \
    -v /etc/coturn/turnserver.conf:/etc/coturn/turnserver.conf:ro \
    -e TURN_USERNAME=${turn_username} \
    -e TURN_PASSWORD=$TURN_PASSWORD \
    ${turn_server_image} \
    turnserver -c /etc/coturn/turnserver.conf -v

echo "TURN server setup completed successfully"

# Verify the container is running
sleep 5
if docker ps | grep -q coturn-server; then
    echo "TURN server is running successfully"
else
    echo "ERROR: TURN server failed to start"
    docker logs coturn-server
    exit 1
fi
