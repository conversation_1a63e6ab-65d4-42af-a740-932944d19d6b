variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
}

variable "region" {
  description = "Google Cloud region"
  type        = string
}

variable "zone" {
  description = "Google Cloud zone"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "vpc_name" {
  description = "Name of the VPC network"
  type        = string
}

variable "subnet_name" {
  description = "Name of the subnet"
  type        = string
}

variable "machine_type" {
  description = "Machine type for the TURN server"
  type        = string
  default     = "e2-small"
}

variable "turn_server_image" {
  description = "Docker image for the TURN server"
  type        = string
}

variable "turn_username" {
  description = "TURN server username"
  type        = string
  sensitive   = true
}

variable "turn_password_secret" {
  description = "Secret Manager secret ID for TURN password"
  type        = string
}

variable "service_account_email" {
  description = "Service account email for the TURN server"
  type        = string
}

variable "labels" {
  description = "Labels to apply to resources"
  type        = map(string)
  default     = {}
}
