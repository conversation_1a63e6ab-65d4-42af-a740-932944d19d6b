variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
}

variable "region" {
  description = "Google Cloud region"
  type        = string
}

variable "zone" {
  description = "Google Cloud zone"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "vpc_name" {
  description = "Name of the VPC network"
  type        = string
}

variable "subnet_name" {
  description = "Name of the subnet"
  type        = string
}

variable "machine_type" {
  description = "Machine type for the media server"
  type        = string
  default     = "e2-standard-2"
}

variable "service_account_email" {
  description = "Service account email for the media server"
  type        = string
}

variable "package_name" {
  description = "Name of the npm package to install"
  type        = string
  default     = "@webrtc-platform/media-server"
}

variable "package_version" {
  description = "Version of the npm package to install"
  type        = string
  default     = "latest"
}

variable "labels" {
  description = "Labels to apply to resources"
  type        = map(string)
  default     = {}
}
