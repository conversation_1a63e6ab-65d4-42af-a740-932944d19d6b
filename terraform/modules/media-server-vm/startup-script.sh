#!/bin/bash

# Startup script for media server VM - npm package installation
set -e

# Configure logging
exec > >(tee /var/log/startup-script.log)
exec 2>&1

echo "Starting media server setup via npm package..."

# Clean up any corrupted apt preferences
rm -f /etc/apt/preferences.d/nodejs /etc/apt/preferences.d/nsolid

# Update package lists and install required packages
apt-get update
apt-get install -y curl wget gnupg2 software-properties-common openssl

# Clean install of Node.js 20 (required for mediasoup)
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt-get install -y nodejs

# Verify Node.js is working
if ! command -v node &> /dev/null; then
    echo "Node.js installation failed, trying alternative method..."
    # Alternative: Install from snap
    apt-get install -y snapd
    snap install node --classic
fi

# Verify installation
if command -v node &> /dev/null; then
    echo "✅ Node.js version: $(node --version)"
    echo "✅ NPM version: $(npm --version)"
else
    echo "❌ Node.js installation failed"
    exit 1
fi

# Create media server directory
mkdir -p /opt/media-server
cd /opt/media-server

# Create SSL certificates directory
mkdir -p /opt/media-server/ssl

# Generate self-signed SSL certificate for HTTPS
openssl req -x509 -newkey rsa:4096 -keyout /opt/media-server/ssl/key.pem -out /opt/media-server/ssl/cert.pem -days 365 -nodes -subj "/C=US/ST=CA/L=SF/O=WebRTC/CN=${announced_ip}"

# Set proper permissions for SSL certificates
chmod 644 /opt/media-server/ssl/cert.pem
chmod 600 /opt/media-server/ssl/key.pem

# Create package.json for the deployment
cat > package.json << EOF
{
  "name": "media-server-deployment",
  "version": "1.0.0",
  "description": "Media server deployment",
  "main": "index.js",
  "scripts": {
    "start": "webrtc-media-server"
  },
  "dependencies": {
    "${package_name}": "${package_version}"
  }
}
EOF

# Configure npm to use private Artifact Registry
echo "Configuring npm for private registry..."
npm config set @webrtc-platform:registry https://us-central1-npm.pkg.dev/switcher-studio-233517/webrtc-platform-dev-npm/

# Create .npmrc with authentication token
echo "Setting up npm authentication..."
NPM_TOKEN=$(gcloud auth print-access-token)
cat > .npmrc << 'EOF'
@webrtc-platform:registry=https://us-central1-npm.pkg.dev/switcher-studio-233517/webrtc-platform-dev-npm/
EOF
echo "//us-central1-npm.pkg.dev/switcher-studio-233517/webrtc-platform-dev-npm/:_authToken=$NPM_TOKEN" >> .npmrc

# Install the media server package directly from npm registry
echo "Installing media server package: ${package_name}@${package_version}"
if npm install ${package_name}@${package_version}; then
    echo "✅ Package installed successfully"
else
    echo "❌ Package installation failed. Trying to install from public npm..."
    # Fallback: try to install a basic express server if private package fails
    npm install express socket.io cors
    echo "⚠️  Installed fallback packages. You'll need to publish the private package."
fi

# Create a symlink for the binary
ln -sf /opt/media-server/node_modules/@webrtc-platform/media-server/server.js /usr/local/bin/webrtc-media-server
chmod +x /usr/local/bin/webrtc-media-server

# Create systemd service for auto-start
cat > /etc/systemd/system/media-server.service << EOF
[Unit]
Description=WebRTC Media Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/media-server
Environment=NODE_ENV=production
Environment=LISTEN_IP=${listen_ip}
Environment=ANNOUNCED_IP=${announced_ip}
Environment=RECORDING_IP=${recording_ip}
Environment=SSL_CERT_PATH=/opt/media-server/ssl/cert.pem
Environment=SSL_KEY_PATH=/opt/media-server/ssl/key.pem
Environment=ENABLE_HTTPS=true
Environment=PORT=8080
Environment=WEBRTC_ENABLE_UDP=true
Environment=WEBRTC_ENABLE_TCP=true
Environment=WEBRTC_PREFER_UDP=true
Environment=WEBRTC_PORT_RANGE_MIN=40000
Environment=WEBRTC_PORT_RANGE_MAX=40100
ExecStart=/usr/bin/node /opt/media-server/node_modules/@webrtc-platform/media-server/server.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
systemctl daemon-reload
systemctl enable media-server
systemctl start media-server

# Wait a moment and check status
sleep 5
systemctl status media-server

echo "Media server setup completed successfully"
echo "Service status:"
systemctl is-active media-server
echo "Logs:"
journalctl -u media-server --no-pager --lines=10
