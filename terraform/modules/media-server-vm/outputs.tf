output "media_server_ip" {
  description = "External IP address of the media server"
  value       = google_compute_address.media_server_ip.address
}

output "media_server_internal_ip" {
  description = "Internal IP address of the media server"
  value       = google_compute_instance.media_server.network_interface[0].network_ip
}

output "media_server_url" {
  description = "URL of the media server"
  value       = "https://${google_compute_address.media_server_ip.address}:8080"
}

output "instance_name" {
  description = "Name of the media server instance"
  value       = google_compute_instance.media_server.name
}

output "instance_zone" {
  description = "Zone of the media server instance"
  value       = google_compute_instance.media_server.zone
}
