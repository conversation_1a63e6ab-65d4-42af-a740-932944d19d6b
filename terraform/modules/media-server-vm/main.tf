# Simple Compute Engine VM for Media Server

# Static IP for media server
resource "google_compute_address" "media_server_ip" {
  name         = "${var.name_prefix}-media-ip"
  region       = var.region
  project      = var.project_id
  address_type = "EXTERNAL"
}

# Media Server Instance
resource "google_compute_instance" "media_server" {
  name         = "${var.name_prefix}-media-server"
  machine_type = var.machine_type
  zone         = var.zone
  project      = var.project_id

  tags = ["media-server", "ssh-server", "http-server", "https-server"]

  boot_disk {
    initialize_params {
      image = "ubuntu-os-cloud/ubuntu-2204-lts"
      size  = 30
      type  = "pd-standard"
    }
  }

  network_interface {
    network    = var.vpc_name
    subnetwork = var.subnet_name
    
    access_config {
      nat_ip = google_compute_address.media_server_ip.address
    }
  }

  service_account {
    email  = var.service_account_email
    scopes = ["cloud-platform"]
  }

  metadata = {
    enable-oslogin = "TRUE"
    startup-script = templatefile("${path.module}/startup-script.sh", {
      listen_ip       = "0.0.0.0"
      announced_ip    = google_compute_address.media_server_ip.address
      recording_ip    = "0.0.0.0"
      package_name    = var.package_name
      package_version = var.package_version
    })
  }

  labels = var.labels

  lifecycle {
    create_before_destroy = true
  }
}

# Firewall rule for WebRTC media ports
resource "google_compute_firewall" "allow_webrtc_media" {
  name    = "${var.name_prefix}-allow-webrtc-media"
  network = var.vpc_name
  project = var.project_id

  allow {
    protocol = "udp"
    ports    = ["32256-65535"]  # WebRTC media port range (mediasoup worker range)
  }

  allow {
    protocol = "tcp"
    ports    = ["32256-65535"]  # WebRTC media port range (TCP fallback)
  }

  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["media-server"]
}

# Firewall rule for media server HTTPS
resource "google_compute_firewall" "allow_media_server_https" {
  name    = "${var.name_prefix}-allow-media-server-https"
  network = var.vpc_name
  project = var.project_id

  allow {
    protocol = "tcp"
    ports    = ["8080", "443"]
  }

  source_ranges = ["0.0.0.0/0"]
  target_tags   = ["media-server"]
}
