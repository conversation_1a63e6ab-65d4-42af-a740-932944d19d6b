# Security Module for WebRTC Platform

# Service Account for Signaling Server
resource "google_service_account" "signaling_server" {
  account_id   = "${var.name_prefix}-signaling"
  display_name = "WebRTC Signaling Server Service Account"
  description  = "Service account for the WebRTC signaling server"
  project      = var.project_id
}

# Service Account for Media Server
resource "google_service_account" "media_server" {
  account_id   = "${var.name_prefix}-media"
  display_name = "WebRTC Media Server Service Account"
  description  = "Service account for the WebRTC media server"
  project      = var.project_id
}

# IAM binding for media server to access Artifact Registry
resource "google_project_iam_member" "media_artifact_registry_reader" {
  project = var.project_id
  role    = "roles/artifactregistry.reader"
  member  = "serviceAccount:${google_service_account.media_server.email}"
}

# Service Account for TURN Server
resource "google_service_account" "turn_server" {
  account_id   = "${var.name_prefix}-turn"
  display_name = "TURN Server Service Account"
  description  = "Service account for the TURN server"
  project      = var.project_id
}

# IAM Roles for Signaling Server
resource "google_project_iam_member" "signaling_logging" {
  project = var.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.signaling_server.email}"
}

resource "google_project_iam_member" "signaling_monitoring" {
  project = var.project_id
  role    = "roles/monitoring.metricWriter"
  member  = "serviceAccount:${google_service_account.signaling_server.email}"
}

resource "google_project_iam_member" "signaling_secretmanager" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.signaling_server.email}"
}

# IAM Roles for Media Server
resource "google_project_iam_member" "media_logging" {
  project = var.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.media_server.email}"
}

resource "google_project_iam_member" "media_monitoring" {
  project = var.project_id
  role    = "roles/monitoring.metricWriter"
  member  = "serviceAccount:${google_service_account.media_server.email}"
}

resource "google_project_iam_member" "media_secretmanager" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.media_server.email}"
}

resource "google_project_iam_member" "media_storage" {
  project = var.project_id
  role    = "roles/storage.objectAdmin"
  member  = "serviceAccount:${google_service_account.media_server.email}"
}

# IAM Roles for TURN Server
resource "google_project_iam_member" "turn_logging" {
  project = var.project_id
  role    = "roles/logging.logWriter"
  member  = "serviceAccount:${google_service_account.turn_server.email}"
}

resource "google_project_iam_member" "turn_monitoring" {
  project = var.project_id
  role    = "roles/monitoring.metricWriter"
  member  = "serviceAccount:${google_service_account.turn_server.email}"
}

resource "google_project_iam_member" "turn_secretmanager" {
  project = var.project_id
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.turn_server.email}"
}

# Secret Manager for TURN credentials
resource "google_secret_manager_secret" "turn_password" {
  secret_id = "${var.name_prefix}-turn-password"
  project   = var.project_id

  replication {
    auto {}
  }

  labels = var.labels
}

resource "google_secret_manager_secret_version" "turn_password" {
  secret      = google_secret_manager_secret.turn_password.id
  secret_data = var.turn_password
}

# Secret for TURN username
resource "google_secret_manager_secret" "turn_username" {
  secret_id = "${var.name_prefix}-turn-username"
  project   = var.project_id

  replication {
    auto {}
  }

  labels = var.labels
}

resource "google_secret_manager_secret_version" "turn_username" {
  secret      = google_secret_manager_secret.turn_username.id
  secret_data = var.turn_username
}
