output "signaling_service_account_email" {
  description = "Email of the signaling server service account"
  value       = google_service_account.signaling_server.email
}

output "media_service_account_email" {
  description = "Email of the media server service account"
  value       = google_service_account.media_server.email
}

output "turn_service_account_email" {
  description = "Email of the TURN server service account"
  value       = google_service_account.turn_server.email
}

output "turn_password_secret_id" {
  description = "Secret Manager secret ID for TURN password"
  value       = google_secret_manager_secret.turn_password.secret_id
}

output "turn_username_secret_id" {
  description = "Secret Manager secret ID for TURN username"
  value       = google_secret_manager_secret.turn_username.secret_id
}
