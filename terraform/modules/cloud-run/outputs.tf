output "signaling_server_url" {
  description = "URL of the signaling server"
  value       = google_cloud_run_service.signaling_server.status[0].url
}

# Note: Media server URL now comes from Compute Engine module

output "signaling_service_name" {
  description = "Name of the signaling server service"
  value       = google_cloud_run_service.signaling_server.name
}

# Note: Media server outputs moved to Compute Engine module

output "vpc_connector_name" {
  description = "Name of the VPC access connector"
  value       = google_vpc_access_connector.connector.name
}
