variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
}

variable "region" {
  description = "Google Cloud region"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "vpc_name" {
  description = "Name of the VPC network"
  type        = string
}

variable "subnet_name" {
  description = "Name of the subnet"
  type        = string
}

variable "signaling_server_image" {
  description = "Docker image for the signaling server"
  type        = string
}

variable "media_server_image" {
  description = "Docker image for the media server"
  type        = string
}

variable "cpu" {
  description = "CPU allocation for Cloud Run services"
  type        = string
  default     = "1"
}

variable "memory" {
  description = "Memory allocation for Cloud Run services"
  type        = string
  default     = "2Gi"
}

variable "max_instances" {
  description = "Maximum number of Cloud Run instances"
  type        = number
  default     = 10
}

variable "signaling_service_account" {
  description = "Service account email for signaling server"
  type        = string
}

variable "media_service_account" {
  description = "Service account email for media server"
  type        = string
}

variable "turn_server_ip" {
  description = "IP address of the TURN server"
  type        = string
}

variable "turn_username" {
  description = "TURN server username"
  type        = string
  sensitive   = true
}

variable "turn_password_secret" {
  description = "Secret Manager secret ID for TURN password"
  type        = string
}

variable "media_server_url" {
  description = "URL of the media server (can be placeholder initially)"
  type        = string
  default     = "https://media-server-placeholder"
}

variable "labels" {
  description = "Labels to apply to resources"
  type        = map(string)
  default     = {}
}
