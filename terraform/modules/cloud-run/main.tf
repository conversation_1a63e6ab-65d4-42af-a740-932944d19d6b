# Cloud Run Module for WebRTC Platform

# Signaling Server Cloud Run Service
resource "google_cloud_run_service" "signaling_server" {
  name     = "${var.name_prefix}-signaling"
  location = var.region
  project  = var.project_id

  template {
    metadata {
      labels = var.labels
      annotations = {
        "autoscaling.knative.dev/maxScale"         = var.max_instances
        "run.googleapis.com/execution-environment" = "gen2"
        "run.googleapis.com/vpc-access-connector"  = google_vpc_access_connector.connector.name
      }
    }

    spec {
      service_account_name = var.signaling_service_account
      
      containers {
        image = var.signaling_server_image
        
        ports {
          container_port = 3001
        }

        resources {
          limits = {
            cpu    = var.cpu
            memory = var.memory
          }
        }



        env {
          name  = "NODE_ENV"
          value = "production"
        }

        env {
          name  = "MEDIA_SERVER_URL"
          value = var.media_server_url
        }

        env {
          name  = "TURN_SERVER_IP"
          value = var.turn_server_ip
        }

        env {
          name  = "TURN_USERNAME"
          value = var.turn_username
        }

        env {
          name = "TURN_PASSWORD"
          value_from {
            secret_key_ref {
              name = var.turn_password_secret
              key  = "latest"
            }
          }
        }

        env {
          name  = "MEDIA_SERVER_URL"
          value = var.media_server_url
        }
      }
    }
  }

  traffic {
    percent         = 100
    latest_revision = true
  }

  depends_on = [google_vpc_access_connector.connector]
}

# Note: Media server moved to Compute Engine module

# VPC Access Connector for Cloud Run Signaling Server
resource "google_vpc_access_connector" "connector" {
  name          = "webrtc-${var.environment}-signaling-conn"
  region        = var.region
  project       = var.project_id
  network       = var.vpc_name
  ip_cidr_range = "********/28"
  
  min_instances = 2
  max_instances = 3
}

# IAM policy to allow unauthenticated access
resource "google_cloud_run_service_iam_member" "signaling_public" {
  service  = google_cloud_run_service.signaling_server.name
  location = google_cloud_run_service.signaling_server.location
  project  = var.project_id
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# Note: Media server IAM moved to Compute Engine module
