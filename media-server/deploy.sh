#!/bin/bash

# Media Server Deployment Script
# Usage: ./deploy.sh [PROJECT_ID] [REGION]

set -e

# Default values
DEFAULT_PROJECT_ID="switcher-studio-233517"
DEFAULT_REGION="us-central1"
SERVICE_NAME="media-server"

# Get project ID and region from arguments or use defaults
PROJECT_ID=${1:-$DEFAULT_PROJECT_ID}
REGION=${2:-$DEFAULT_REGION}

echo "🚀 Deploying Media Server to GCP Cloud Run"
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo "Service Name: $SERVICE_NAME"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ gcloud CLI is not installed. Please install it first."
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install it first."
    exit 1
fi

# Set the project
echo "📋 Setting GCP project..."
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Build and deploy using Cloud Build
echo "🏗️ Building and deploying with Cloud Build..."
gcloud builds submit --config cloudbuild.yaml \
    --substitutions=_PROJECT_ID=$PROJECT_ID,_REGION=$REGION

echo "✅ Deployment complete!"
echo "🌐 Your service should be available at:"
gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)"

echo ""
echo "📝 To view logs:"
echo "gcloud logs tail --service=$SERVICE_NAME"
echo ""
echo "🔧 To update environment variables:"
echo "gcloud run services update $SERVICE_NAME --region=$REGION --set-env-vars KEY=VALUE"
