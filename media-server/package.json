{"name": "@webrtc-platform/media-server", "description": "WebRTC to RTMP Media Server", "version": "1.0.0", "main": "server.js", "bin": {"webrtc-media-server": "./server.js"}, "scripts": {"start": "node server.js"}, "files": ["server.js", "config.production.js"], "dependencies": {"browserify": "^16.0.0", "cors": "^2.8.5", "express": "^4.0.0", "ffmpeg-static": "^4.0.0", "mediasoup": "^3.18.0", "mediasoup-client": "~3.6.0", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "socket.io-promise": "^1.0.0"}, "devDependencies": {"eslint": "^6.0.0", "prettier": "~2.0.0"}, "publishConfig": {"registry": "https://us-central1-npm.pkg.dev/switcher-studio-233517/webrtc-platform-dev-npm/"}}