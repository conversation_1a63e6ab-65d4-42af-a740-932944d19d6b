apiVersion: v1
kind: ConfigMap
metadata:
  name: media-server-config
  namespace: default
data:
  NODE_ENV: "production"
  LISTEN_IP: "0.0.0.0"
  ANNOUNCED_IP: "TBD"  # Will be updated with LoadBalancer IP
  RECORDING_IP: "0.0.0.0"
  WEBRTC_ENABLE_UDP: "true"
  WEBRTC_ENABLE_TCP: "true"
  WEBRTC_PREFER_UDP: "true"
  WEBRTC_PORT_RANGE_MIN: "40000"
  WEBRTC_PORT_RANGE_MAX: "40100"
  ENABLE_HTTPS: "true"
  PORT: "8080"

---
apiVersion: v1
kind: Secret
metadata:
  name: media-server-ssl
  namespace: default
type: Opaque

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: media-server
  namespace: default
  annotations:
    iam.gke.io/gcp-service-account: <EMAIL>

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: media-server
  namespace: default
  labels:
    app: media-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: media-server
  template:
    metadata:
      labels:
        app: media-server
    spec:
      serviceAccountName: media-server
      initContainers:
      - name: ssl-init
        image: alpine/openssl:latest
        command:
        - sh
        - -c
        - |
          openssl req -x509 -newkey rsa:4096 -keyout /ssl/key.pem -out /ssl/cert.pem -days 365 -nodes -subj '/C=US/ST=CA/L=SF/O=WebRTC/CN=media-server'
          chmod 644 /ssl/cert.pem
          chmod 600 /ssl/key.pem
        volumeMounts:
        - name: ssl-certs
          mountPath: /ssl
      containers:
      - name: media-server
        image: us-central1-docker.pkg.dev/switcher-studio-233517/webrtc-platform-dev-repo/media-server:latest
        ports:
        - containerPort: 8080
          protocol: TCP
        envFrom:
        - configMapRef:
            name: media-server-config
        env:
        - name: SSL_CERT_PATH
          value: "/app/ssl/cert.pem"
        - name: SSL_KEY_PATH
          value: "/app/ssl/key.pem"
        volumeMounts:
        - name: ssl-certs
          mountPath: /app/ssl
          readOnly: true
        resources:
          limits:
            cpu: "2"
            memory: "4Gi"
          requests:
            cpu: "1"
            memory: "2Gi"
        livenessProbe:
          httpGet:
            path: /
            port: 8080
            scheme: HTTPS
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 8080
            scheme: HTTPS
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: ssl-certs
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: media-server-service
  namespace: default
  annotations:
    cloud.google.com/load-balancer-type: "External"
spec:
  selector:
    app: media-server
  ports:
  - name: https
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: LoadBalancer
