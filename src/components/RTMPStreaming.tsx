import React, { useCallback, useEffect, useState } from 'react';
import { useMediasoup } from '../hooks/useMediasoup';

interface RTMPStreamingProps {
  canvasStream: MediaStream | null;
  isHost: boolean;
  onRestartCanvasStream?: () => void;
}

export const RTMPStreaming: React.FC<RTMPStreamingProps> = ({
  canvasStream,
  isHost,
  onRestartCanvasStream,
}) => {

  const {
    state,
    connect,
    startProducing,
    stopProducing,
    stopRTMPStream,
  } = useMediasoup();

  const [isStarting, setIsStarting] = useState(false);
  const [isStopping, setIsStopping] = useState(false);
  const [isRestartingCanvas, setIsRestartingCanvas] = useState(false);
  const [isPreparing, setIsPreparing] = useState(false);

  // Auto-connect when component mounts if host
  useEffect(() => {
    if (isHost && !state.isConnected) {
      connect().catch(console.error);
    }
  }, [isHost, state.isConnected, connect]);

  // Clear preparing state when streaming actually starts
  useEffect(() => {
    if (state.isStreaming && isPreparing) {
      console.log('✅ Streaming confirmed, clearing preparing state');
      setIsPreparing(false);
    }
  }, [state.isStreaming, isPreparing]);

  const handleStartStreaming = useCallback(async () => {
    if (isStarting || isPreparing) return; // Prevent double-clicks

    setIsStarting(true);
    setIsPreparing(true);

    try {
      // Connect first if not connected
      if (!state.isConnected) {
        console.log('🔗 Connecting to media server...');
        await connect();
      }

      // Check canvas stream status
      if (!canvasStream) {
        throw new Error('Canvas stream is not available. Please make sure the video compositor is running.');
      }

      // Check if canvas stream tracks are valid
      const tracks = canvasStream.getTracks();
      const hasEndedTracks = tracks.some(track => track.readyState === 'ended');

      if (hasEndedTracks) {
        console.log('⚠️ Canvas stream has ended tracks, restarting...');
        if (onRestartCanvasStream) {
          setIsRestartingCanvas(true);
          onRestartCanvasStream();

          // Wait for the new stream with polling
          let attempts = 0;
          const maxAttempts = 20; // 2 seconds max
          while (attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;

            if (canvasStream && canvasStream.getTracks().length > 0 &&
                canvasStream.getTracks().every(track => track.readyState === 'live')) {
              console.log('✅ Fresh canvas stream ready');
              break;
            }
          }

          setIsRestartingCanvas(false);

          if (!canvasStream || canvasStream.getTracks().some(track => track.readyState === 'ended')) {
            throw new Error('Unable to get fresh canvas stream. Please refresh the page.');
          }
        } else {
          throw new Error('Canvas stream has ended tracks. Please refresh the page.');
        }
      }

      console.log('✅ Canvas stream is ready with', tracks.length, 'live tracks');

      // Start producing (RTMP will auto-start when video producer is created)
      if (!state.isProducing) {
        console.log('🎬 Starting video production (RTMP will auto-start)...');
        await startProducing(canvasStream);

        // Wait for streaming to actually start (polling for streaming status)
        console.log('⏳ Waiting for RTMP streaming to start...');
        let streamingAttempts = 0;
        const maxStreamingAttempts = 30; // 3 seconds max
        while (streamingAttempts < maxStreamingAttempts && !state.isStreaming) {
          await new Promise(resolve => setTimeout(resolve, 100));
          streamingAttempts++;
        }

        if (!state.isStreaming) {
          console.log('⚠️ RTMP streaming did not start within expected time');
        } else {
          console.log('✅ RTMP streaming confirmed active');
        }
      }
    } catch (error) {
      console.error('Failed to start streaming:', error);
    } finally {
      setIsStarting(false);
      setIsRestartingCanvas(false);
      setIsPreparing(false);
    }
  }, [connect, startProducing, canvasStream, state.isConnected, state.isProducing, state.isStreaming, onRestartCanvasStream, isStarting, isPreparing]);

  const handleStopStreaming = useCallback(async () => {
    if (isStopping) return; // Prevent double-clicks

    setIsStopping(true);
    try {
      // Stop RTMP streaming first
      if (state.isStreaming) {
        console.log('⏹️ Stopping RTMP stream...');
        await stopRTMPStream();
      }

      // Stop producing (but keep canvas stream alive)
      if (state.isProducing) {
        console.log('⏹️ Stopping video production...');
        await stopProducing();
      }

      // Note: We don't stop the canvas stream here - it should keep running
      // so it's immediately available for the next start
      console.log('✅ Streaming stopped, canvas stream preserved');
    } catch (error) {
      console.error('Failed to stop streaming:', error);
    } finally {
      setIsStopping(false);
    }
  }, [stopRTMPStream, stopProducing, state.isStreaming, state.isProducing, isStopping]);

  if (!isHost) {
    return null; // Only hosts can control RTMP streaming
  }

  const isStreaming = state.isStreaming;

  return (
    <div className="bg-gray-800/50 backdrop-blur-lg border border-gray-700 rounded-2xl p-4">
      <h3 className="text-lg font-bold text-white mb-4">RTMP Streaming</h3>

      {/* Streaming Status */}
      <div className="mb-4">
        <div className="flex items-center space-x-3">
          <div
            className={`w-4 h-4 rounded-full ${
              isStreaming ? 'bg-red-500 animate-pulse' : 'bg-gray-500'
            }`}
          />
          <span className="text-white font-medium">
            {isStreaming ? 'Streaming' : 'Not Streaming'}
          </span>
        </div>
      </div>

      {/* Error Display */}
      {state.error && (
        <div className="mb-4 p-3 bg-red-500/20 border border-red-500/50 text-red-300 rounded-lg">
          <strong>Error:</strong> {state.error}
        </div>
      )}

      {/* Stream Controls */}
      <div className="space-y-3">
        {!isStreaming ? (
          <button
            onClick={handleStartStreaming}
            disabled={isStarting || isRestartingCanvas || isPreparing}
            className="w-full px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:bg-gray-600 disabled:cursor-not-allowed font-medium transition-colors flex items-center justify-center space-x-2"
          >
            {isPreparing || isStarting || isRestartingCanvas ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>
                  {isRestartingCanvas ? 'Restarting Canvas...' :
                   isPreparing ? 'Preparing...' : 'Starting...'}
                </span>
              </>
            ) : (
              <>
                <span>🔴</span>
                <span>Start Streaming</span>
              </>
            )}
          </button>
        ) : (
          <button
            onClick={handleStopStreaming}
            disabled={isStopping}
            className="w-full px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 font-medium transition-colors flex items-center justify-center space-x-2"
          >
            {isStopping ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Stopping...</span>
              </>
            ) : (
              <>
                <span>⏹️</span>
                <span>Stop Streaming</span>
              </>
            )}
          </button>
        )}

        {/* Show warning only if no canvas stream or all tracks are ended, and not restarting */}
        {(!canvasStream || (canvasStream && canvasStream.getTracks().length === 0) ||
          (canvasStream && canvasStream.getTracks().every(track => track.readyState === 'ended'))) &&
         !isRestartingCanvas && (
          <div className="text-sm text-amber-300 bg-amber-500/20 p-3 rounded-lg">
            ⚠️ No video composite available. Make sure the video compositor is running.
          </div>
        )}

        {/* Show status messages */}
        {isPreparing && !isRestartingCanvas && (
          <div className="text-sm text-blue-300 bg-blue-500/20 p-3 rounded-lg">
            ⚙️ Setting up streaming pipeline...
          </div>
        )}

        {isRestartingCanvas && (
          <div className="text-sm text-blue-300 bg-blue-500/20 p-3 rounded-lg">
            🔄 Restarting video composite...
          </div>
        )}

        {!state.isConnected && !isRestartingCanvas && !isPreparing && (
          <div className="text-sm text-blue-300 bg-blue-500/20 p-3 rounded-lg">
            🔗 Connecting to media server...
          </div>
        )}
      </div>

      {/* RTMP Configuration Info */}
      <div className="mt-4 p-3 bg-blue-500/20 rounded-lg">
        <div className="text-sm font-medium text-blue-300 mb-1">RTMP Configuration</div>
        <div className="text-xs text-blue-400">
          Streaming to: https://www.twitch.tv/mikeswitcherstudio
        </div>
      </div>
    </div>
  );
};
