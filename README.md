# Switcher.AI - Professional WebRTC Streaming Platform

A production-ready multi-peer WebRTC streaming application with WebGL compositing and RTMP output capabilities.

## Features

### Core Functionality
- **Multi-peer WebRTC Streaming**: Real-time video and audio streaming between multiple participants
- **WebGL Video Compositing**: Hardware-accelerated video processing for combining multiple feeds
- **RTMP Output**: Stream composite video to external platforms (Twitch, YouTube, etc.)
- **Real-time Audio Mixing**: Synchronized audio from multiple sources
- **Professional UI**: Broadcast-quality interface with modern design

### Host Features
- Create and manage streaming rooms
- Composite multiple video feeds with different layouts (Grid, Focus, PIP)
- Real-time participant management
- RTMP streaming configuration
- Connection quality monitoring

### Participant Features
- Join rooms with simple room codes
- Camera and microphone controls
- Real-time connection status
- Low-latency streaming to host

## Architecture

### System Components
1. **Frontend Application** - React-based client interface
2. **Signaling Server** - WebRTC signaling and room management
3. **Media Server** - Mediasoup-based WebRTC-to-RTMP processing
4. **TURN Server** - NAT traversal for WebRTC connections

### Tech Stack

#### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **WebRTC** for peer-to-peer connections
- **WebGL Canvas** for video compositing
- **Socket.io Client** for signaling

#### Backend Services
- **Signaling Server**: Node.js with Express and Socket.io
- **Media Server**: Mediasoup with FFmpeg for RTMP streaming
- **TURN Server**: Coturn for NAT traversal

## Getting Started

### Prerequisites
- Node.js 18+
- Modern browser with WebRTC support
- Camera and microphone access
- Google Cloud SDK (for deployment)
- Docker (for containerized deployment)

### Development Setup

1. Clone the repository
2. Install dependencies:
```bash
npm install
```

3. Start all development servers:
```bash
npm run dev
```

This will start:
- **Client** (Vite) on port 5173
- **Signaling Server** on port 3001
- **Media Server** on port 8080

### Local Production Testing
```bash
npm run build:serve
```

This builds the frontend and starts the server serving both the website and API on port 3001.

## Deployment

### Current Deployment Architecture
The application consists of multiple services that can be deployed independently:

1. **Main Application** (Frontend + Signaling Server) → Cloud Run
2. **Media Server** → Cloud Run with Cloud Build
3. **TURN Server** → Google Compute Engine

### Quick Deployment (Current Method)

#### 1. Deploy Main Application
```bash
npm run deploy
```

#### 2. Deploy Media Server
```bash
cd media-server
./deploy.sh
```

#### 3. Deploy TURN Server
```bash
cd turn-server
./deploy-gce.sh
./configure-client.sh
```

### Infrastructure as Code (Terraform) - ✅ Available
We now provide Terraform for comprehensive infrastructure management:

#### Quick Start with Terraform
```bash
cd terraform
./scripts/deploy-all.sh dev your-project-id
```

#### Features
- **Environment Management**: Separate dev/staging/prod configurations
- **Automated Deployment**: Single script deploys all services
- **Infrastructure Versioning**: Track changes with Git
- **Consistent Configuration**: Environment variables managed centrally
- **Monitoring & Alerting**: Built-in monitoring dashboards

#### Terraform Structure
```bash
terraform/
├── environments/          # Environment-specific configs
│   ├── dev/              # Development environment
│   ├── staging/          # Staging environment
│   └── prod/             # Production environment
├── modules/              # Reusable Terraform modules
├── scripts/              # Deployment and utility scripts
└── README.md            # Detailed Terraform documentation
```

See [terraform/README.md](terraform/README.md) for detailed instructions.

## Usage

1. **Create a Room**: Click "Create Room" to start as a host
2. **Join a Room**: Enter a room ID to join as a participant
3. **Configure Streaming**: Hosts can set up RTMP streaming in the settings panel
4. **Manage Layout**: Choose between Grid, Focus, or Picture-in-Picture layouts

## System Architecture

### Service Communication Flow
```
[Frontend] ←→ [Signaling Server] ←→ [Media Server]
     ↓              ↓                    ↓
[WebRTC Peers] ←→ [TURN Server] ←→ [RTMP Output]
```

### WebRTC Flow
1. Participants join rooms via the signaling server
2. Peer connections are established using ICE candidates and TURN server
3. Media streams are exchanged between peers
4. Host receives all participant streams for compositing
5. Composite stream is sent to media server for RTMP output

### Video Compositing
- Uses WebGL with vertex and fragment shaders for hardware acceleration
- Supports multiple layout modes with smooth transitions
- Optimized for 1920x1080 output resolution at 3-6 Mbps bitrate
- Canvas capture stream for WebRTC transmission

### Media Server (Mediasoup)
- Receives WebRTC streams from the main application
- Processes video/audio using FFmpeg
- Outputs to RTMP endpoints (Twitch, YouTube, etc.)
- Handles recording and stream monitoring

### TURN Server (Coturn)
- Provides NAT traversal for WebRTC connections
- Deployed on Google Compute Engine for UDP support
- Configured with static credentials (development) or dynamic auth (production)

## Configuration

### Environment Variables
- `PORT`: Server port (default: 3001)
- `VITE_TURN_SERVER`: Custom TURN server IP/domain (optional)
- `VITE_TURN_USERNAME`: TURN server username (default: webrtc)
- `VITE_TURN_PASSWORD`: TURN server password (default: webrtc123)

### TURN Server Setup

For reliable WebRTC connections across different networks, deploy your own TURN server:

```bash
# Deploy TURN server to Google Cloud
cd turn-server
./deploy-gce.sh

# Configure client to use deployed server
./configure-client.sh
```

See `turn-server/README.md` for detailed TURN server documentation.

### RTMP Setup
Configure your streaming platform's RTMP settings:
- **Twitch**: `rtmp://live.twitch.tv/live/` + stream key
- **YouTube**: `rtmp://a.rtmp.youtube.com/live2/` + stream key
- **Custom**: Your RTMP server URL + stream key

## Development

### Project Structure
```
src/                    # Frontend React application
├── components/         # React components
├── contexts/          # React contexts (Connection management)
├── hooks/             # Custom React hooks
├── App.tsx           # Main application component
└── main.tsx          # Application entry point

server/                # Signaling server
├── index.js          # WebRTC signaling server
├── Dockerfile        # Container configuration
└── package.json      # Server dependencies

media-server/          # WebRTC-to-RTMP media processing
├── server.js         # Mediasoup media server
├── config.js         # Development configuration
├── config.production.js # Production configuration
├── Dockerfile        # Container configuration
├── cloudbuild.yaml   # Google Cloud Build configuration
├── deploy.sh         # Deployment script
└── package.json      # Media server dependencies

turn-server/           # NAT traversal server
├── Dockerfile        # Coturn TURN server container
├── turnserver.conf   # Coturn configuration
├── deploy-gce.sh     # GCE deployment script
├── configure-client.sh # Client configuration script
├── docker-compose.yml # Local development setup
└── README.md         # TURN server documentation

terraform/             # Infrastructure as Code (planned)
├── environments/     # Environment-specific configurations
├── modules/          # Reusable Terraform modules
└── main.tf          # Main infrastructure definition
```

### Key Components
- **WelcomeScreen**: Room creation and joining interface
- **HostInterface**: Main hosting dashboard with compositing
- **ParticipantInterface**: Participant streaming interface
- **VideoCompositor**: WebGL-based video compositing engine
- **ConnectionContext**: WebRTC connection management

## Production Deployment

### Current Deployment Status
- ✅ **Main Application**: Deployed to Cloud Run
- ✅ **Media Server**: Deployed to Cloud Run with Cloud Build
- ✅ **TURN Server**: Deployed to Google Compute Engine
- 🚧 **Infrastructure as Code**: Terraform implementation in progress

### Service URLs and Configuration
After deployment, services are available at:
- **Main App**: `https://poc-webrtc-[hash]-uc.a.run.app`
- **Media Server**: `https://media-server-[hash]-uc.a.run.app`
- **TURN Server**: `[GCE-EXTERNAL-IP]:3478`

### Environment Variables
Configure these environment variables for production:

```bash
# Main Application
PORT=3001
NODE_ENV=production
MEDIA_SERVER_URL=https://media-server-[hash]-uc.a.run.app

# TURN Server Configuration (auto-configured by deploy scripts)
VITE_TURN_SERVER=[GCE-EXTERNAL-IP]
VITE_TURN_USERNAME=webrtc
VITE_TURN_PASSWORD=webrtc123

# Google Cloud Project
GOOGLE_CLOUD_PROJECT=your-project-id
REGION=us-central1
```

### Manual Deployment Steps
If not using the deployment scripts:

1. **Build and deploy main application**:
```bash
npm run build
gcloud run deploy poc-webrtc --source . --platform managed --region us-central1 --allow-unauthenticated
```

2. **Deploy media server**:
```bash
cd media-server
gcloud builds submit --config cloudbuild.yaml
```

3. **Deploy TURN server**:
```bash
cd turn-server
./deploy-gce.sh
```

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

WebRTC and Canvas API support required.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details.